"""
测试脚本：测试模块化版本的服务器是否能正常运行
"""
import asyncio
import importlib.util
import sys

async def test_imports():
    """测试是否能成功导入所有必需的模块"""
    modules_to_test = [
        "modules.database",
        "modules.agents",
        "modules.prompt",
        "modules.tools",
        "modules.session"
    ]
    
    all_passed = True
    for module_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=["*"])
            print(f"✓ 成功导入模块: {module_name}")
        except ImportError as e:
            print(f"✗ 导入模块失败: {module_name}")
            print(f"  错误: {str(e)}")
            all_passed = False
    
    return all_passed


async def test_server():
    """测试服务器是否能够启动"""
    try:
        # 尝试导入server_new.py
        spec = importlib.util.spec_from_file_location("server_new", "server_new.py")
        server_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(server_module)
        print("✓ 服务器模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 服务器模块导入失败: {str(e)}")
        return False

async def main():
    print("开始测试模块化版本...\n")
    
    # 测试导入
    print("测试模块导入:")
    imports_ok = await test_imports()
    if not imports_ok:
        print("\n导入测试失败。请检查模块依赖关系。")
        return False
    
    print("\n测试服务器导入:")
    server_ok = await test_server()
    if not server_ok:
        print("\n服务器测试失败。请检查server_new.py文件。")
        return False
    
    print("\n所有测试通过！系统已准备好迁移。")
    print("请运行 python migrate.py 完成迁移。")
    return True

if __name__ == "__main__":
    if asyncio.run(main()):
        sys.exit(0)
    else:
        sys.exit(1)

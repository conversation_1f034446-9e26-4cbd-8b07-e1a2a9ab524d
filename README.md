# aily-services

Aily智能助手服务，基于多智能体架构的Arduino开发助手平台。

## 项目简介

Aily-services是一个基于多智能体协作的Arduino开发助手平台，采用Plan-and-Execute模式，通过多个专门化的AI代理协同工作，为用户提供从项目规划到代码生成的全方位Arduino开发支持。

## 智能代理架构

本项目采用多智能体协作架构，包含以下专门化的AI代理：

### 核心规划代理

#### plannerAgent (规划代理)
- **作用**: 负责总体任务规划和协调，分析用户需求并将任务分配给合适的执行代理
- **模型**: gemini-2.5-pro
- **提示词文件**: `planner_agent.txt`
- **功能**: 
  - 分析用户需求
  - 制定执行计划
  - 任务分配和调度
  - 协调各个执行代理

### 项目分析与推荐代理

#### projectAnalysisAgent (项目分析代理)
- **作用**: 专门负责需求分析阶段，深入分析和整理用户项目需求，为开发板和库推荐提供详细的技术基础
- **模型**: gpt-4o
- **提示词文件**: `project_analysis_agent.txt`
- **功能**:
  - 用户需求深度分析
  - 技术可行性评估
  - 项目架构设计建议

#### boardRecommendationAgent (开发板推荐代理)
- **作用**: 负责推荐合适的开发板
- **模型**: gpt-4o
- **提示词文件**: `board_recommendation_agent.txt`
- **功能**:
  - 根据项目需求推荐开发板
  - 硬件兼容性分析
  - 性能和成本评估

#### libraryRecommendationAgent (库推荐代理)
- **作用**: 负责推荐合适的Arduino库
- **模型**: gpt-4o
- **提示词文件**: `library_recommendation_agent.txt`
- **功能**:
  - 库功能匹配分析
  - 依赖关系管理
  - 版本兼容性检查

### 项目管理代理

#### projectCreationAgent (项目创建代理)
- **作用**: 负责创建新的Arduino项目
- **模型**: gpt-4o
- **提示词文件**: `project_creation_agent.txt`
- **功能**:
  - 项目结构生成
  - 初始代码框架创建
  - 配置文件设置

#### libraryInstallationAgent (库安装代理)
- **作用**: 负责安装和管理Arduino库
- **模型**: gpt-4o
- **提示词文件**: `library_installation_agent.txt`
- **功能**:
  - 库依赖安装
  - 版本管理
  - 库配置优化

### 代码生成与修复代理

#### blocklyGenerationAgent (Blockly代码生成代理)
- **作用**: 负责生成Blockly可视化编程代码
- **模型**: gemini-2.5-pro
- **提示词文件**: `blockly_generation_agent.txt`
- **功能**:
  - Blockly代码块生成
  - 可视化编程逻辑构建
  - Arduino代码转换

#### blocklyRepairAgent (Blockly修复代理)
- **作用**: 负责修复Blockly代码中的错误
- **模型**: gemini-2.5-pro
- **提示词文件**: `blockly_repair_agent.txt`
- **功能**:
  - Blockly代码错误诊断
  - 逻辑错误修复
  - 代码优化建议

#### compilationErrorRepairAgent (编译错误修复代理)
- **作用**: 负责修复Arduino代码编译错误
- **模型**: gpt-4o
- **提示词文件**: `compilation_error_repair_agent.txt`
- **功能**:
  - 编译错误分析
  - 语法错误修复
  - 依赖问题解决

### 工具与辅助代理

#### arduinoLibraryAnalysisAgent (Arduino库分析代理)
- **作用**: 专门负责分析Arduino库代码结构和功能，为转换为Blockly库做技术准备
- **模型**: gemini-2.5-pro
- **提示词文件**: `arduino_library_analysis_agent.txt`
- **功能**:
  - 库代码结构分析
  - API接口提取
  - Blockly转换准备

#### fileOperationAgent (文件操作代理)
- **作用**: 负责各种文件操作任务
- **模型**: gpt-4o
- **提示词文件**: `file_operation_agent.txt`
- **功能**:
  - 文件读写操作
  - 目录管理
  - 文件格式转换

#### contextAgent (上下文代理)
- **作用**: 用于获取和管理上下文信息
- **模型**: gpt-4o
- **提示词文件**: `context_agent.txt`
- **功能**:
  - 上下文信息提取
  - 对话历史管理
  - 状态信息维护

## 工作流程

1. **用户请求** → plannerAgent (需求分析和任务规划)
2. **任务分配** → 具体执行代理 (专门化任务处理)
3. **任务完成** → plannerAgent (结果整合和下一步规划)
4. **迭代优化** → 根据用户反馈调整执行策略

## 技术特点

- **多智能体协作**: 基于Plan-and-Execute模式的智能体协作
- **专门化分工**: 每个代理专注于特定领域的任务
- **动态调度**: 根据任务需求动态选择合适的代理
- **状态管理**: 支持对话状态的持久化和恢复
- **前端工具集成**: 与前端UI无缝集成，支持实时交互

## 部署说明

### 创建并启动容器

```bash
docker-compose up -d
```

### 停止并删除容器
> 更改.env中的环境变量后也需要重启

```bash
docker-compose down
```

### 其他操作

```bash
docker-compose start
docker-compose stop
docker-compose restart
```

## 环境配置

在运行前请确保配置以下环境变量：
- `OPENAI_API_KEY`: OpenAI API密钥
- `OPENAI_BASE_URL`: OpenAI API基础URL
- 其他相关的AI模型API配置

## 目录结构

- `ai/`: AI代理核心模块
  - `agents/`: 各个智能代理实现
  - `modules/`: 核心功能模块
  - `prompts/`: 代理提示词模板
  - `state/`: 对话状态存储
- `db/`: 数据库相关文件
- `gateway/`: API网关配置
- `data/`: 数据存储目录
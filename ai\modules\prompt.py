"""
提示词管理和系统配置模块
"""
import os
import glob
import aiofiles
from loguru import logger


SYSTEM_PROMPT_PATH = "prompts"


def load_system_prompt() -> str:
    """加载系统提示词"""
    if os.path.exists(SYSTEM_PROMPT_PATH + "/system_prompt.txt"):
        print("从文件加载系统提示词")
        with open(SYSTEM_PROMPT_PATH, "r", encoding="utf-8") as f:
            return f.read()
    # 回退到环境变量
    return os.environ.get("SYSTEM_PROMPT", "你是一个智能助手，负责回答用户的问题。请尽量提供准确和有用的信息。")


def load_system_prompt_file(file_name: str) -> str:
    """加载指定的系统提示词文件"""
    file_path = os.path.join(SYSTEM_PROMPT_PATH, file_name)
    if os.path.exists(file_path):
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()

    return "You are a helpful assistant."

def save_system_prompt(prompt: str):
    """保存系统提示词"""
    with open(SYSTEM_PROMPT_PATH, "w", encoding="utf-8") as f:
        f.write(prompt)


async def get_prompt_files():
    """获取prompts文件夹下的所有.txt文件列表"""
    prompt_files = []
    prompts_dir = "prompts"
    if os.path.exists(prompts_dir) and os.path.isdir(prompts_dir):
        # 获取所有.txt文件
        txt_files = glob.glob(os.path.join(prompts_dir, "*.txt"))
        for file_path in txt_files:
            file_name = os.path.basename(file_path)
            prompt_files.append({
                "name": file_name,
                "path": file_path
            })
    
    return prompt_files


async def get_prompt_content(file_name: str):
    """获取指定提示词文件的内容"""
    # 确保文件名安全，防止目录遍历攻击
    safe_file_name = os.path.basename(file_name)
    file_path = os.path.join("prompts", safe_file_name)
    
    if not os.path.exists(file_path) or not file_path.endswith(".txt"):
        raise FileNotFoundError("文件不存在或不是有效的提示词文件")
    
    async with aiofiles.open(file_path, "r", encoding="utf-8") as file:
        content = await file.read()
        
    return {
        "name": safe_file_name,
        "path": file_path,
        "content": content
    }


async def save_prompt_file(file_name: str, content: bytes):
    """保存提示词文件"""
    # 确保文件名安全，只接受.txt文件
    file_name = os.path.basename(file_name)
    if not file_name.endswith(".txt"):
        raise ValueError("只支持保存.txt文件")
    
    # 确保prompts目录存在
    prompts_dir = "prompts"
    os.makedirs(prompts_dir, exist_ok=True)
    
    file_path = os.path.join(prompts_dir, file_name)
    
    # 保存文件
    async with aiofiles.open(file_path, "wb") as out_file:
        await out_file.write(content)
        
    return {
        "name": file_name,
        "path": file_path
    }

# Aily Chat 工具使用指南

本文档包含Aily Chat 工具的使用示例和详细说明。

## 工具概览

| 工具名称 | 主要功能 | 适用场景 |
|----------|----------|----------|
| create_project | 创建新项目 | 项目初始化 |
| execute_command | 执行系统命令 | 安装包、运行脚本 |
| get_context | 获取上下文信息 | 环境检查、状态获取 |
| file_operations | 文件操作 | 文件读写、目录管理 |
| fetch | 网络请求 | API调用、资源下载 |

## 1. create_project 工具

### 基本语法
```json
{
  "board": {
    "name": "开发板名称",
    "nickname": "开发板昵称", 
    "version": "版本号"
  }
}
```

### 使用示例
```json
{
  "board": {
    "name": "@aily-project/board-jinniu_board",
    "nickname": "金牛创翼板",
    "version": "0.0.1"
  }
}
```

## 2. execute_command 工具

### 基本语法
```json
{
  "command": "要执行的命令",
  "cwd": "工作目录(可选)"
}
```

### 常用示例

| 操作类型 | 示例命令 | JSON格式 |
|----------|----------|----------|
| 安装npm包 | npm i @aily-project/board-jinniu_board | `{"command": "npm i @aily-project/board-jinniu_board"}` |
| 指定目录操作 | ls -la | `{"command": "ls -la", "cwd": "/home/<USER>/projects"}` |
| 查看版本 | node --version | `{"command": "node --version"}` |

## 3. get_context 工具

### 基本语法
```json
{
  "info_type": "信息类型(可选)"
}
```

### 信息类型选项

| 类型 | 说明 | 返回内容 |
|------|------|----------|
| "all" | 获取所有上下文信息 | 完整的系统状态 |
| "project" | 仅获取项目相关信息 | 项目配置和依赖 |
| 未指定 | 默认返回核心信息 | 基本系统状态 |
### 典型返回结果示例
```json
{
  "project": {
    "name": "My Project",
    "path": "/path/to/root/project",
    "rootFolder": "/path/to/root",
    "dependencies": {
      "@aily-project/board-jinniu_board": "0.0.1",
      "@aily-project/lib-core-io": "1.0.0",
      "@aily-project/lib-core-logic": "0.0.1",
      "@aily-project/lib-core-loop": "0.0.1"
    }
  },
  "editingMode": {
    "mode": "blockly"
  }
}
```

## 4. file_operations 工具

### 基本语法
```json
{
  "operation": "操作类型",
  "path": "文件或文件夹路径",
  "name": "文件或文件夹名称(可选)",
  "content": "文件内容(可选)",
  "is_folder": "是否为文件夹(可选，布尔值)"
}
```

### 操作类型表

| 操作类型 | 说明 | 必需参数 | 可选参数 |
|----------|------|----------|----------|
| exists | 检查文件/文件夹是否存在 | path | name, is_folder |
| list | 列出文件夹内容 | path | - |
| read | 读取文件内容 | path | name |
| create | 创建文件/文件夹 | path | name, content, is_folder |
| edit | 编辑文件 | path, content | - |
| delete | 删除文件/文件夹 | path | is_folder |
| rename | 重命名(创建备份) | path | is_folder |

### 使用示例

#### 文件检查操作
```json
// 检查文件是否存在
{
  "operation": "exists",
  "path": "./package.json"
}

// 检查文件夹是否存在
{
  "operation": "exists",
  "path": "./",
  "name": "src",
  "is_folder": true
}
```

#### 文件读取操作
```json
// 列出文件夹内容
{
  "operation": "list",
  "path": "./src"
}

// 读取文件内容
{
  "operation": "read",
  "path": "./",
  "name": "package.json"
}
```

#### 文件创建操作
```json
// 使用路径和名称创建文件
{
  "operation": "create",
  "path": "./src",
  "name": "example.ts",
  "content": "console.log('Hello, World!');"
}

// 直接使用完整路径创建文件
{
  "operation": "create",
  "path": "./src/example.ts",
  "content": "console.log('Hello, World!');"
}

// 创建文件夹
{
  "operation": "create",
  "path": "./new-folder",
  "is_folder": true
}
```

#### 文件编辑和删除
```json
// 编辑文件
{
  "operation": "edit",
  "path": "./src/example.ts",
  "content": "console.log('Updated content');"
}

// 删除文件
{
  "operation": "delete",
  "path": "./src/example.ts"
}

// 重命名文件（创建备份）
{
  "operation": "rename",
  "path": "./src/old-file.ts"
}
```
```

#### 重命名文件夹（实际是删除并创建备份）
```json
{
  "operation": "rename",
  "path": "./old-folder",
  "is_folder": true
}
```

## 5. fetch 工具

### 基本语法
```json
{
  "url": "要请求的URL",
  "method": "HTTP方法(可选)",
  "headers": "请求头(可选，JSON对象)",
  "body": "请求体(可选)",
  "timeout": "超时时间(可选)",
  "maxSize": "最大文件大小(可选)",
  "responseType": "响应类型(可选)"
}
```

### 请求类型表

| 请求类型 | Method | 用途 | 示例场景 |
|----------|--------|------|----------|
| 数据获取 | GET | 获取API数据 | 获取开发板列表 |
| 数据提交 | POST | 提交表单数据 | 用户注册 |
| 文件下载 | GET | 下载文件 | 下载固件 |
| 认证请求 | GET/POST | 需要授权的请求 | 访问私有API |

### 使用示例

#### 基础GET请求
```json
// 获取网页内容
{
  "url": "https://example.com"
}

// 获取JSON API数据
{
  "url": "https://api.example.com/data",
  "method": "GET",
  "responseType": "json"
}
```

#### 文件下载
```json
{
  "url": "https://example.com/large-file.zip",
  "responseType": "blob",
  "maxSize": 104857600
}
```

#### POST请求和认证
```json
// POST请求
{
  "url": "https://api.example.com/submit",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "key": "value"
  },
  "responseType": "json"
}

// 带认证的请求
{
  "url": "https://api.example.com/protected",
  "headers": {
    "Authorization": "Bearer token123",
    "User-Agent": "MyApp/1.0"
  }
}
```

## 工具使用注意事项

### 最佳实践表

| 工具类型 | 关键注意事项 | 推荐做法 |
|----------|-------------|----------|
| create_project | 确保开发板信息完整 | 包含完整的包名、昵称和版本号 |
| execute_command | 注意工作目录设置 | 明确指定cwd路径 |
| get_context | 选择合适的信息类型 | 根据需要选择"all"或"project" |
| file_operations | 明确文件/文件夹类型 | 创建文件夹时设置is_folder为true |
| fetch | 设置合理的限制 | 下载大文件时设置maxSize限制 |

## 工具使用状态反馈

每次调用工具进行操作时，**必须告知用户**：

### 状态标准格式

```aily-state
{"state":"状态类型","text": "简要说明", "id": "唯一标识"}
```

### 状态类型说明

| 状态类型 | 说明 | 使用时机 |
|----------|------|----------|
| doing | 正在执行 | 工具调用开始时 |
| done | 执行成功 | 工具调用成功完成 |
| warn | 执行警告 | 有警告但可继续 |
| error | 执行失败 | 工具调用失败 |

### 状态反馈示例

**获取开发板列表：**

```aily-state
{"state": "doing", "text": "获取开发板列表", "id": "fetch_board_json"}
```

```aily-state
{"state": "done", "text": "开发板列表获取完成", "id": "fetch_board_json"}
```

**获取库列表：**

```aily-state
{"state": "doing", "text": "获取库列表", "id": "fetch_library_json"}
```

```aily-state
{"state": "error", "text": "获取超时", "id": "fetch_library_json"}
```

### 用户交互要求

在使用工具与用户交互时，请：
- 使用友好的表情符号增强可读性 🔧
- 提供清晰的操作步骤说明 📝
- 及时反馈工具执行状态 ⚡
- 用表格形式展示复杂信息结构 📊

### 重要提示

- 代码块要成对出现（doing → done/warn/error）
- 代码块前后都必须要有换行符
- 说明描述内容要简洁且控制在10字以内
- 每组状态的id需要保证统一

## 错误处理策略

当工具调用出现问题时：
- **错误处理**：当工具返回错误时，提供具体的解决建议
- **超时处理**：当请求超时时，建议用户重试或检查网络连接
- **权限问题**：当遇到权限错误时，指导用户检查文件权限或API访问权限

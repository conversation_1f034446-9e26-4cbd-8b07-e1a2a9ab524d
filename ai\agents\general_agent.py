import os

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient


model_client = OpenAIChatCompletionClient(
    model="gpt-4.1",
    api_key=os.environ.get("OPENAI_API_KEY"),
    api_base=os.environ.get("OPENAI_API_BASE"),
)


agent = AssistantAgent(
    name="general_agent",
    description="这是一个普通的助手agent，能够处理一般的任务和问题。",
    model_client=model_client,
    system_message="""
        你是一个通用助手，能够处理各种任务和问题。
        请根据用户的请求提供帮助。
        请确保回答清晰、准确，并符合用户的需求。
        完成回复后，以"APPROVE"结束回复
    """,
)

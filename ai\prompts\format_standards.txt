# Aily 系统输出格式规范

## 规范概述

本文档定义了 **Aily 系统所有功能模块的标准输出格式**，确保用户界面的一致性和专业性。所有 AI 代理和前端组件都必须严格遵循此规范。

**重要：在与用户交互时，AI 代理必须使用结构化格式、适当的 emoji 表情和表格来提升用户体验。**

### 核心原则
- **格式严格性**：所有标记符号必须精确匹配，不可修改
- **状态透明性**：操作过程状态实时反馈给用户  
- **数据完整性**：推荐信息必须原样返回，保证数据一致性
- **视觉友好性**：在用户交互中使用 emoji 和结构化格式提升体验

---

## 状态反馈规范

### 反馈机制说明
用于在工具调用过程中向用户实时反馈操作状态，包括：**准备阶段(doing)**、**完成阶段(done)**、**警告(warn)**、**错误(error)**。

### 格式定义

```aily-state
{"state": "状态类型", "text": "简要说明", "id": "操作标识"}
```

### 参数说明
| 参数 | 类型 | 说明 | 限制 |
|------|------|------|------|
| **state** | `string` | 状态类型：`doing`/`done`/`warn`/`error` | 必填 |
| **text** | `string` | 状态说明文本 | ≤10字，简洁明了 |
| **id** | `string` | 操作唯一标识符 | 同组操作保持一致 |

### 使用场景与示例

#### 场景1：开发板信息获取

```aily-state
{"state": "doing", "text": "获取开发板列表", "id": "fetch_boards"}
```

```aily-state
{"state": "done", "text": "开发板列表已获取", "id": "fetch_boards"}
```

#### 场景2：库资源查询

```aily-state
{"state": "doing", "text": "查询库资源", "id": "fetch_libraries"}
```

```aily-state
{"state": "error", "text": "网络超时", "id": "fetch_libraries"}
```

#### 场景3：项目创建

```aily-state
{"state": "doing", "text": "创建项目结构", "id": "create_project"}
```

```aily-state
{"state": "warn", "text": "目录已存在", "id": "create_project"}
```

```aily-state
{"state": "done", "text": "项目创建完成", "id": "create_project"}
```

---

## 开发板推荐格式

### 使用场景
用于向用户推荐最适合的开发板，基于项目需求和技术要求进行智能匹配。

### 格式定义

```aily-board
{开发板JSON对象}
```

### 数据结构说明
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| **name** | `string` | 开发板包名 | `@aily-project/board-arduino_uno` |
| **nickname** | `string` | 友好显示名称 | `Arduino UNO R3` |
| **version** | `string` | 版本号 | `1.0.2` |
| **description** | `string` | 详细描述 | 开发板特性和规格说明 |
| **author** | `string` | 作者/厂商 | `Arduino` |
| **brand** | `string` | 品牌 | `Arduino` |
| **url** | `string` | 参考链接 | 官方文档或购买链接 |
| **compatibility** | `string` | 兼容性说明 | `Arduino IDE, PlatformIO` |
| **img** | `string` | 图片文件名 | `arduino_uno.png` |
| **disabled** | `boolean` | 是否禁用 | `false` |

### 完整示例

```aily-board
{
    "name": "@aily-project/board-arduino_uno",
    "nickname": "Arduino UNO R3",
    "version": "1.0.2",
    "description": "Arduino UNO R3 是一款基于ATmega328P的开源电子原型平台，包含数字I/O口14个（其中6个支持PWM输出），模拟输入口6个，16MHz晶振，USB接口，电源接口，ICSP接口等。",
    "author": "Arduino",
    "brand": "Arduino",
    "url": "https://arduino.me/s/board?aid=698",
    "compatibility": "Arduino IDE, PlatformIO",
    "img": "arduino_uno.png",
    "disabled": false
}
```

---

## 库推荐格式

### 使用场景
用于推荐项目所需的功能库和组件，支持复杂的兼容性和关键词系统。

### 格式定义

```aily-library
{库JSON对象}
```

### 数据结构说明
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| **name** | `string` | 库包名 | `@aily-project/lib-sensor` |
| **nickname** | `string` | 友好显示名称 | `传感器驱动库` |
| **version** | `string` | 版本号 | `1.2.0` |
| **description** | `string` | 功能描述 | 库的详细功能说明 |
| **author** | `string` | 作者 | `Aily Team` |
| **compatibility** | `object` | 兼容性配置 | 详见下表 |
| **keywords** | `array` | 关键词标签 | `["sensor", "arduino"]` |
| **tested** | `boolean` | 是否已测试 | `true` |
| **icon** | `string` | 图标 | `sensor.png` |

### 兼容性对象结构
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| **core** | `array` | 支持的核心平台 | `["arduino:avr", "esp32:esp32"]` |
| **voltage** | `array` | 支持的电压 | `[3.3, 5]` |

### 完整示例

```aily-library
{
    "name": "@aily-project/lib-shiftregister",
    "nickname": "移位寄存器驱动库",
    "version": "0.0.1",
    "description": "移位寄存器74HC595控制库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板",
    "author": "Simsso",
    "compatibility": {
        "core": [
            "arduino:avr",
            "esp32:esp32",
            "esp8266:esp8266"
        ],
        "voltage": [3.3, 5]
    },
    "keywords": [
        "aily",
        "blockly",
        "shiftregister",
        "74HC595",
        "shift_register",
        "移位寄存器",
        "io扩展"
    ],
    "tested": true,
    "icon": "shift_register.png"
}
```

---

## 用户交互按钮格式

### 使用场景
当需要用户进行确认、选择或触发特定动作时使用，提供直观的交互界面。

### 格式定义

```aily-button
[{"text": "按钮文本", "action": "动作标识"}]
```

### 参数说明
| 参数 | 类型 | 说明 | 限制 |
|------|------|------|------|
| **text** | `string` | 按钮显示文本 | ≤5字，简洁明了 |
| **action** | `string` | 触发的动作标识 | 系统预定义的动作类型 |

### 常用动作类型
| 动作标识 | 说明 | 使用场景 |
|----------|------|----------|
| `create_project` | 创建新项目 | 项目初始化 |
| `install_library` | 安装库 | 库管理 |
| `compile_code` | 编译代码 | 代码构建 |
| `upload_code` | 上传代码 | 部署到硬件 |
| `open_docs` | 打开文档 | 查看帮助 |

### 使用示例

#### 单按钮示例

```aily-button
[{"text": "创建项目", "action": "create_project"}]
```

#### 多按钮示例

```aily-button
[
    {"text": "安装库", "action": "install_library"},
    {"text": "查看文档", "action": "open_docs"}
]
```

"""
会话管理和流处理模块
"""
import asyncio
import json
from typing import AsyncGenerator
from loguru import logger
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import TextMessage, UserInputRequestedEvent, ModelClientStreamingChunkEvent
from .database import get_conversation_history

# 全局会话存储
sessions = {}
response_queues = {}
user_input_queues = {}
tool_result_queues = {}  # 用于存储前端工具调用的结果


async def stream_generator(session_id: str) -> AsyncGenerator[str, None]:
    """流数据生成器，用于向前端发送SSE事件流"""
    if session_id not in response_queues:
        response_queues[session_id] = asyncio.Queue()
    queue = response_queues[session_id]
    yield json.dumps({"type": "connected", "session_id": session_id}) + "\n"
    try:
        while True:
            # 检查 session_id 是否还在 response_queues 中
            if session_id not in response_queues:
                # 如果已移除，停止生成器
                break

            try:
                data = await asyncio.wait_for(queue.get(), timeout=30.0)
                yield data + "\n"
            except asyncio.TimeoutError:
                pass
                # 可以在这里发送心跳包
                # yield json.dumps({"type": "ping"}) + "\n"
    except asyncio.CancelledError:
        pass


async def close_stream(session_id: str):
    """关闭指定会话的数据流连接"""
    if session_id in response_queues:
        # 向队列发送关闭消息，通知客户端连接即将关闭
        await response_queues[session_id].put(
            json.dumps({"type": "stream_closed", "message": "流连接已被服务器关闭"})
        )

        # 从队列字典中移除该会话的队列
        del response_queues[session_id]


async def reset_team(session_id: str) -> bool:
    """重置团队到初始状态，用于团队运行失败后的重新启动
    
    Args:
        session_id: 会话ID
        
    Returns:
        bool: 重置是否成功
    """
    try:
        if session_id in sessions and "team" in sessions[session_id]:
            team = sessions[session_id]["team"]
            
            # 调用Team.reset()方法重置团队和所有参与者到初始状态
            await team.reset()
            
            logger.info(f"团队 {session_id} 已成功重置到初始状态")
            
            # 可选：清理旧的状态文件
            # await clear_team_state(session_id)
            
            return True
        else:
            logger.warning(f"会话 {session_id} 不存在或没有团队实例")
            return False
            
    except Exception as e:
        logger.error(f"重置团队 {session_id} 失败: {e}", exc_info=True)
        return False


async def restart_team_after_failure(session_id: str, tools: list, external_termination) -> bool:
    """团队运行失败后的完整重启流程
    
    这个函数会：
    1. 重置现有团队到初始状态
    2. 如果重置失败，则重新创建团队
    3. 保存新的团队状态
    
    Args:
        session_id: 会话ID
        tools: 工具列表
        external_termination: 外部终止条件
        
    Returns:
        bool: 重启是否成功
    """
    try:
        # 首先尝试重置现有团队
        reset_success = await reset_team(session_id)
        
        if not reset_success:
            # 如果重置失败，重新创建团队
            logger.info(f"重置失败，为会话 {session_id} 重新创建团队")
            
            # 导入get_team函数（避免循环导入）
            from modules.agents import get_team
            
            # 重新创建团队
            new_team = await get_team(session_id, tools, external_termination)
            sessions[session_id]["team"] = new_team
            
            logger.info(f"为会话 {session_id} 成功创建新团队")
        
        # 保存团队状态
        team = sessions[session_id]["team"]
        from modules.agents import save_team_state
        await save_team_state(session_id, team)
        
        # 通知前端团队已重启
        if session_id in response_queues:
            await response_queues[session_id].put(
                json.dumps({
                    "type": "team_restarted", 
                    "message": "团队已重新启动，可以继续对话"
                })
            )
        
        return True
        
    except Exception as e:
        logger.error(f"重启团队 {session_id} 失败: {e}", exc_info=True)
        
        # 通知前端重启失败
        if session_id in response_queues:
            await response_queues[session_id].put(
                json.dumps({
                    "type": "team_restart_failed", 
                    "message": f"团队重启失败: {str(e)}"
                })
            )
        
        return False


async def process_message(session_id: str, message: TextMessage, save_conversation, save_team_state):
    """处理用户消息 - 参考app_team.py的实现方式"""

    # 获取队列和团队
    queue = response_queues[session_id]
    team = sessions[session_id]["team"]

    # history
    history = await get_conversation_history(session_id)

    try:
        # 参考app_team.py：运行团队流，用户消息作为任务
        stream = team.run_stream(task=message)
        async for item_message in stream:
            if isinstance(item_message, TaskResult):
                continue

            # 跳过工具调用相关的内部消息
            # if isinstance(item_message, ToolCallRequestEvent):
            #     continue

            # if isinstance(item_message, ToolCallExecutionEvent):
            #     continue

            # if isinstance(item_message, ToolCallSummaryMessage):
            #     continue

            # if isinstance(item_message, ThoughtEvent):
            #     continue

            if not isinstance(item_message, UserInputRequestedEvent) and not isinstance(
                item_message, ModelClientStreamingChunkEvent
            ):
                history.append(item_message.model_dump())

            # 对于用户消息，只有当它不是原始输入时才发送（避免回显）
            if item_message.source == "user" and item_message.content == message.content:
                continue

            queue.put_nowait(json.dumps(item_message.dump()))

            # 将消息放入响应队列发送给前端

            # 开启流式传输后models_usage会为空，最后才会有个总结性的消息，包含了model_usage以及完整的消息
            # message_data = {
            #     "type": (
            #         "agent_response"
            #         if item_message.source != "user" and not item_message.models_usage else "user_message"
            #     ),
            #         "data": item_message.to_text(),
            #         "source": item_message.source,
            # }

            # queue.put_nowait(json.dumps(message_data))

            # queue.put_nowait(json.dumps(item_message.dump()))

            # 记录非用户消息（用户消息已经在上面记录过了）
            # if not isinstance(item_message, TaskResult) and item_message.source != "user" and item_message.models_usage:
            #     await save_conversation(session_id, item_message)

        queue.put_nowait(json.dumps({"type": "TaskCompleted", "stop_reason": item_message.stop_reason}))
    except Exception as e:
        error_message = str(e)

        # 检查是否是 Anthropic 500 错误
        if "InternalServerError" in error_message and "500" in error_message:
            error_message = "AI 服务暂时不可用，请稍后重试。"
        elif "Unhandled message in agent container" in error_message:
            error_message = "消息处理失败，请重新发送您的消息。"

        logger.error("处理消息时出错: {}", error_message, exc_info=True)

        # 检查是否需要重启团队
        should_restart = await should_restart_team_on_error(e)

        if should_restart:
            logger.info(f"检测到严重错误，尝试重启团队 {session_id}")

            # 通知前端正在重启团队
            if session_id in response_queues:
                response_queues[session_id].put_nowait(
                    json.dumps({
                        "type": "team_restarting", 
                        "message": "检测到错误，正在重启团队..."
                    })
                )

            # 尝试重启团队
            # 这里需要获取tools和external_termination，可以从sessions中获取或作为参数传递
            from modules.agents import get_team

            tools = getattr(sessions[session_id], 'tools', [])  # 如果有保存的话
            external_termination = None  # 需要从外部传入或获取

            restart_success = await restart_team_after_failure(
                session_id, tools, external_termination
            )

            if restart_success:
                error_message += " 团队已自动重启，您可以重新发送消息。"
            else:
                error_message += " 团队重启失败，请尝试重新开始会话。"

        if session_id in response_queues:
            logger.info("error: 已将出错传给前端")
            response_queues[session_id].put_nowait(
                json.dumps({"type": "error", "message": error_message})
            )
        else:
            logger.info("error: 未将出错传给前端")

    # 记录任务结果
    await save_conversation(session_id, history)

    # 保存团队状态
    await save_team_state(session_id, team)


async def should_restart_team_on_error(error: Exception) -> bool:
    """判断是否应该基于错误类型重启团队
    
    Args:
        error: 捕获的异常
        
    Returns:
        bool: 是否应该重启团队
    """
    error_message = str(error)
    
    # 定义需要重启的错误模式
    restart_patterns = [
        "agent container",  # agent容器相关错误
        "timeout",  # 超时错误
        "connection",  # 连接错误
        "memory",  # 内存相关错误
        "state",  # 状态相关错误
        "corrupted",  # 数据损坏
        "deadlock",  # 死锁
    ]
    
    # 检查错误消息是否匹配需要重启的模式
    for pattern in restart_patterns:
        if pattern.lower() in error_message.lower():
            return True
    
    # 检查特定的异常类型
    restart_exception_types = [
        "RuntimeError",
        "ConnectionError",
        "TimeoutError",
        "MemoryError",
    ]
    
    for exc_type in restart_exception_types:
        if exc_type in str(type(error)):
            return True
    
    return False

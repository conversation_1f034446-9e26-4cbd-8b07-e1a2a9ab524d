import os

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient


# 设置模型客户端
model_client = OpenAIChatCompletionClient(
    model="gpt-4o",  # 使用高级模型以支持所有功能
    api_key=os.environ.get("OPENAI_API_KEY"),
    api_base=os.environ.get("OPENAI_API_BASE"),
)


agent = AssistantAgent(
    name="audit_agent",
    description="这个是审核专家，负责审核最终agent的回答是否符合要求。",
    model_client=model_client,
    system_message="""
        你是一位全能的审核专家，负责审核agent的回答是否符合要求。你的任务包括：
        1. 确保agent的回答符合预期的格式和内容要求。
        2. 检查回答是否包含敏感信息或不当内容。
        3. 确认回答是否准确、完整，并符合用户的需求。
        4. 如果回答不符合要求，提供具体的修改建议。
        5. 如果回答符合要求，直接返回"APPROVE"。
        6. 如果回答不符合要求，返回"REJECT"并附上修改建议。

        注意以上是需要检查的项，只需要返回"APPROVE"或"REJECT"和修改建议，不需要其他内容。
    """,
)

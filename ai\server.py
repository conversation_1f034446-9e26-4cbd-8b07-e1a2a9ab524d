"""
主服务器文件，包含FastAPI应用和路由定义
"""
from fastapi import FastAPI, BackgroundTasks, UploadFile, File
from fastapi.responses import StreamingResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import json
import uuid
import asyncio
import os
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.conditions import ExternalTermination

# 导入自定义模块
from modules.database import startup_db, shutdown_db, save_user_conversation, get_conversation_history
from modules.agents import get_team, save_team_state
from modules.prompt import get_prompt_files, save_prompt_file
from modules.session import (
    sessions, 
    response_queues, 
    user_input_queues, 
    tool_result_queues,
    stream_generator,
    close_stream,
    process_message,
    reset_team,
    restart_team_after_failure,
)

# 设置日志
logger.add(f"logs/server_{int(time.time())}.log", rotation="1 MB", level="INFO", backtrace=True, diagnose=True)

# 外部中断
external_terminations = {}

# 创建FastAPI应用
app = FastAPI()

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)


# 定义请求和响应模型
class MessageRequest(BaseModel):
    content: str


class ResponseModel(BaseModel):
    status: str
    data: Optional[Any] = None
    message: Optional[str] = None


class SystemPromptRequest(BaseModel):
    prompt: str


class PromptFileResponse(BaseModel):
    name: str
    path: str
    content: Optional[str] = None


class ToolObject(BaseModel):
    name: str
    description: str
    input_schema: Dict[str, Any]


class SessionRequest(BaseModel):
    session_id: Optional[str] = None
    tools: Optional[List[ToolObject]] = None


class ConversationRecord(BaseModel):
    role: str
    content: str
    timestamp: datetime


# 启动和关闭事件处理
@app.on_event("startup")
async def on_startup():
    await startup_db()


@app.on_event("shutdown")
async def on_shutdown():
    await shutdown_db()


@app.get("/api/v1/stream/{session_id}")
async def stream_endpoint(session_id: str):
    """建立流式连接，用于向前端发送消息"""
    return StreamingResponse(
        stream_generator(session_id), media_type="application/x-ndjson"
    )


@app.post("/api/v1/close_stream/{session_id}", response_model=ResponseModel)
async def close_stream_api(session_id: str):
    """关闭指定会话的数据流连接"""
    await close_stream(session_id)
    return {"status": "success", "message": "流连接已关闭"}


@app.post("/api/v1/stop_session/{session_id}", response_model=ResponseModel)
async def stop_session_api(session_id: str):
    """停止指定会话的所有活动"""

    if session_id not in sessions:
        return {"status": "error", "message": "会话不存在"}

    external_termination = external_terminations.get(session_id)
    if not external_termination:
        return {"status": "error", "message": "会话未设置外部终止条件"}

    # 触发外部终止条件
    external_termination.set()

    return {"status": "success", "message": "会话已停止"}


@app.post("/api/v1/start_session", response_model=ResponseModel)
async def start_session(request: SessionRequest):
    """开始新会话或恢复现有会话"""
    session_id = request.session_id
    if not session_id:
        session_id = str(uuid.uuid4())

    logger.info("开始会话: {}", session_id)

    response_queues[session_id] = asyncio.Queue()
    user_input_queues[session_id] = asyncio.Queue()

    if session_id in external_terminations:
        external_termination = external_terminations[session_id]
    else:
        external_termination = ExternalTermination()
        external_terminations[session_id] = external_termination

    # 获取或创建团队
    team = await get_team(session_id, request.tools, external_termination)
    sessions[session_id] = {"team": team}
    return {"status": "success", "data": session_id}


@app.post("/api/v1/close_session/{session_id}", response_model=ResponseModel)
async def close_session_api(session_id: str):
    """完全关闭会话，清理所有相关资源"""
    # 关闭流连接
    await close_stream(session_id)

    # 清理会话数据
    if session_id in sessions:
        del sessions[session_id]

    # 清理用户输入队列
    if session_id in user_input_queues:
        del user_input_queues[session_id]

    return {"status": "success", "message": "会话已完全关闭"}


@app.post("/api/v1/reset_team/{session_id}", response_model=ResponseModel)
async def reset_team_api(session_id: str):
    """重置团队到初始状态"""
    if session_id not in sessions:
        return {"status": "error", "message": "会话不存在"}
    
    success = await reset_team(session_id)
    if success:
        return {"status": "success", "message": "团队已重置到初始状态"}
    else:
        return {"status": "error", "message": "团队重置失败"}


@app.post("/api/v1/restart_team/{session_id}", response_model=ResponseModel)
async def restart_team_api(session_id: str, request: SessionRequest):
    """重启团队（完整重启流程）"""
    if session_id not in sessions:
        return {"status": "error", "message": "会话不存在"}
    
    # 获取外部终止条件
    external_termination = external_terminations.get(session_id)
    if not external_termination:
        external_termination = ExternalTermination()
        external_terminations[session_id] = external_termination
    
    success = await restart_team_after_failure(
        session_id, 
        request.tools, 
        external_termination
    )
    
    if success:
        return {"status": "success", "message": "团队已成功重启"}
    else:
        return {"status": "error", "message": "团队重启失败"}


@app.post("/api/v1/send_message/{session_id}", response_model=ResponseModel)
async def send_message(
    session_id: str, message: MessageRequest, background_tasks: BackgroundTasks
):
    """发送消息到指定会话"""
    if session_id not in sessions:
        return {"status": "error", "message": "会话不存在"}

    try:
        content_obj = json.loads(message.content)
        if isinstance(content_obj, dict):
            if content_obj.get("type") == "tool_result":
                tool_id = content_obj.get("tool_id")
                if (
                    session_id in tool_result_queues
                    and tool_id in tool_result_queues[session_id]
                ):

                    # 将结果放入工具结果队列
                    result = {
                        "content": content_obj.get("content", ""),
                        "is_error": content_obj.get("is_error", False),
                    }
                    await tool_result_queues[session_id][tool_id].put(result)
                    logger.info(f"接收到工具 {tool_id} 的执行结果")
                    return {"status": "success"}
            elif content_obj.get("type") == "user_input":
                # 处理用户输入请求
                user_input = content_obj.get("content", "")
                if session_id in user_input_queues:
                    # 将用户输入放入用户输入队列
                    await user_input_queues[session_id].put(user_input)
                    logger.info(f"接收到用户输入: {user_input}")
                    return {"status": "success", "message": "用户输入已记录"}
    except (json.JSONDecodeError, ValueError, TypeError):
        # 不是JSON或不是工具调用结果，按正常消息处理
        pass

    user_message = TextMessage(content=message.content, source="user")
    print(f"Received message: {user_message.content}")

    # 创建一个异步任务来处理消息
    background_tasks.add_task(
        process_message,
        session_id,
        user_message,
        save_user_conversation,
        save_team_state,
    )

    return {"status": "success", "message": "消息已发送，正在处理"}


@app.get("/api/v1/conversation_history/{session_id}", response_model=ResponseModel)
async def get_conversation_history_api(session_id: str):
    """获取指定会话的所有对话历史记录，按时间顺序排列"""
    try:
        history = await get_conversation_history(session_id)
        return {"status": "success", "data": history}
    except Exception as e:
        return {"status": "error", "message": f"获取对话历史记录失败: {str(e)}"}


@app.get("/api/v1/prompts", response_model=ResponseModel)
async def get_prompts_api():
    """获取所有提示词文件列表"""
    try:
        prompt_files = await get_prompt_files()
        return {"status": "success", "data": prompt_files}
    except Exception as e:
        logger.error(f"获取提示词文件列表失败: {str(e)}")
        return {"status": "error", "message": f"获取提示词文件列表失败: {str(e)}"}


@app.get("/api/v1/prompts/{file_name}")
async def get_prompt_file_api(file_name: str):
    """获取指定提示词文件的内容 - 直接返回文本内容而非JSON"""
    try:
        # 确保文件名安全，防止目录遍历攻击
        safe_file_name = os.path.basename(file_name)
        file_path = os.path.join("prompts", safe_file_name)
        
        if not os.path.exists(file_path) or not file_path.endswith(".txt"):
            return {"status": "error", "message": "文件不存在或不是有效的提示词文件"}
        
        # 直接返回文本文件
        return FileResponse(file_path, media_type="text/plain", filename=safe_file_name)
    except Exception as e:
        logger.error(f"读取提示词文件失败: {str(e)}")
        return {"status": "error", "message": f"读取提示词文件失败: {str(e)}"}


@app.post("/api/v1/prompts", response_model=ResponseModel)
async def upload_prompt_file(file: UploadFile = File(...)):
    """上传提示词文件到prompts文件夹"""
    try:
        # 读取上传的文件内容
        content = await file.read()
        
        # 保存文件
        result = await save_prompt_file(file.filename, content)
            
        return {
            "status": "success", 
            "message": f"文件 {file.filename} 上传成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"上传提示词文件失败: {str(e)}")
        return {"status": "error", "message": f"上传提示词文件失败: {str(e)}"}


@app.get("/api/v1/download/prompts/{file_name}")
async def download_prompt(file_name: str):
    """下载提示词文件"""
    try:
        file_path = os.path.join("prompts", file_name)
        if not os.path.exists(file_path):
            return {"status": "error", "message": "文件不存在"}

        return FileResponse(file_path, media_type="text/plain", filename=file_name)
    except Exception as e:
        return {"status": "error", "message": f"下载提示词文件失败: {str(e)}"}


if __name__ == "__main__":
    import uvicorn
    import signal
    import sys

    def signal_handler(sig, frame):
        print("\n正在关闭服务器...")
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    uvicorn.run(app, host="0.0.0.0", port=8000)

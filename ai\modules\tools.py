"""
工具相关功能模块
"""
import os
import asyncio
import aiofiles
import json
from pydantic import BaseModel, create_model
from typing import Dict, Any
from loguru import logger
from autogen_core.tools import BaseTool
from autogen_core.models import FunctionExecutionResult
from autogen_core import CancellationToken


class ToolResult(BaseModel):
    """工具执行结果模型"""
    content: str


class FrontendTool(BaseTool):
    """前端工具适配器，将工具对象转换为BaseTool"""

    def __init__(
        self, name: str, description: str, input_schema: Dict[str, Any], frontend_call
    ):
        # 从input_schema动态创建参数模型
        properties = input_schema.get("properties", {})
        required_fields = input_schema.get("required", [])  # 获取必需字段列表
        fields = {}
        for field_name, field_info in properties.items():
            field_type = str
            if field_info.get("type") == "integer":
                field_type = int
            elif field_info.get("type") == "boolean":
                field_type = bool

            # 如果字段在required列表中，强制设置为必需
            if field_name in required_fields:
                default = ...
            else:
                default = field_info.get("default", ...)

            fields[field_name] = (field_type, default)

        dynamic_model = create_model("DynamicArgs", **fields)

        self._frontend_call = frontend_call
        self._tool_id = name
        super().__init__(
            args_type=dynamic_model,
            return_type=ToolResult,
            name=name,
            description=description,
        )

    async def run(self, args: Any, cancellation_token: CancellationToken) -> ToolResult:
        """执行工具，调用frontend_call"""

        # 创建一个类BaseTool的对象，用于传递给frontend_call
        class ToolObj:
            def __init__(self, id, name, args):
                self.id = id
                self.name = name
                self.args = args
                # 为了向后兼容，也设置 arguments 属性
                self.arguments = args

        tool_obj = ToolObj(
            id=self._tool_id,
            name=self.name,
            args=args.model_dump() if hasattr(args, "model_dump") else dict(args),
        )

        _, result = await self._frontend_call(tool_obj)
        return ToolResult(content=result.content)


async def frontend_call(tool, sessions, tool_result_queues, response_queues):
    """处理前端工具调用"""

    logger.info("======== tool call ========")

    # 确保 tool.id 不为 None，如果为 None 则使用 tool.name 作为备用
    tool_id = tool.id if tool.id is not None else tool.name

    # 从上下文中获取当前session_id
    session_id = None
    for sid, session_data in sessions.items():
        if "current_tool" in session_data and session_data["current_tool"] == tool_id:
            session_id = sid
            break

    if not session_id:
        # 找不到session，可能是因为工具调用没有正确关联到会话
        logger.error(f"工具调用无法确定会话ID: {tool.name}")
        return (
            tool,
            FunctionExecutionResult(
                content="工具调用失败：无法确定会话上下文",
                call_id=tool_id,
                is_error=True,
                name=tool.name,
            ),
        )

    # 创建一个队列来接收工具执行结果
    if session_id not in tool_result_queues:
        tool_result_queues[session_id] = {}
    tool_result_queues[session_id][tool_id] = asyncio.Queue()

    # 向前端发送工具调用请求
    if session_id in response_queues:

        # 记录详细的工具调用信息，便于调试
        logger.info(f"工具调用详情: name={tool.name}, id={tool_id}")
        tool_msg = {
            "type": "tool_call_request",
            "tool_id": tool_id,
            "tool_name": tool.name,
            "tool_args": tool.arguments,
        }

        await response_queues[session_id].put(
            json.dumps(tool_msg)
        )

        logger.info(f"已发送工具调用请求到前端: {tool.name}: {tool_msg}")

        # 等待前端执行结果
        try:
            result = await asyncio.wait_for(
                tool_result_queues[session_id][tool_id].get(),
                timeout=60.0,  # 设置等待超时时间
            )

            # 清理队列
            del tool_result_queues[session_id][tool_id]
            if not tool_result_queues[session_id]:
                del tool_result_queues[session_id]

            # logger.info(f"收到前端工具执行结果: {tool.name}: {result}")

            return (
                tool,
                FunctionExecutionResult(
                    content=result.get("content", ""),
                    call_id=tool_id,
                    is_error=result.get("is_error", False),
                    name=tool.name,
                ),
            )
        except asyncio.TimeoutError:
            logger.error(f"前端工具调用超时: {tool.name}")
            return (
                tool,
                FunctionExecutionResult(
                    content="工具调用超时，请稍后重试",
                    call_id=tool_id,
                    is_error=True,
                    name=tool.name,
                ),
            )
    else:
        logger.error(f"会话 {session_id} 没有可用的响应队列")
        return (
            tool,
            FunctionExecutionResult(
                content="工具调用失败：会话通信通道不可用",
                call_id=tool_id,
                is_error=True,
                name=tool.name,
            ),
        )


import aiohttp


async def backend_fetch_board_json() -> str:
    """获取最新的开发板数据

    Returns:
        最新的开发板数据(JSON字符串)
    """
    url = "https://blockly.diandeng.tech/boards.json"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            return await response.text()


async def backend_fetch_library_json() -> str:
    """获取最新的库数据

    Returns:
        最新的库数据(JSON字符串)
    """
    url = "https://blockly.diandeng.tech/libraries.json"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            return await response.text()


async def backend_fetch_examples_json() -> str:
    """获取最新的示例数据

    Returns:
        最新的示例数据(JSON字符串)
    """
    url = "https://blockly.diandeng.tech/examples.json"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            return await response.text()


async def backend_read_file(file_path: str) -> str:
    """读取指定文件内容

    Args:
        file_path (str): 文件路径

    Returns:
        str: 文件内容
    """
    try:
        async with aiofiles.open(file_path, mode='r', encoding='utf-8') as f:
            return await f.read()
    except Exception as e:
        logger.error(f"读取文件失败: {file_path}, 错误: {str(e)}")
        return ""


async def backend_write_file(file_path: str, content: str) -> bool:
    """写入内容到指定文件

    Args:
        file_path (str): 文件路径
        content (str): 要写入的内容

    Returns:
        bool: 是否写入成功
    """
    try:
        async with aiofiles.open(file_path, mode='w', encoding='utf-8') as f:
            await f.write(content)
        return True
    except Exception as e:
        logger.error(f"写入文件失败: {file_path}, 错误: {str(e)}")
        return False


async def backend_mkdir(directory: str) -> bool:
    """创建目录

    Args:
        directory (str): 目录路径

    Returns:
        bool: 是否创建成功
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {directory}, 错误: {str(e)}")
        return False

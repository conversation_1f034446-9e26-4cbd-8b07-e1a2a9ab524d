services:
  ai-service:
    build: ./ai
    image: aily/ai-service:dev
    env_file:
      - .env.dev
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8000:8000"
    restart: always
    volumes:
      # 开发环境挂载所有代码，方便热重载
      - ./ai:/app
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
      - POSTGRES_MULTIPLE_DATABASES=userdb,aidb,kbdb
    ports:
      - "15435:5432"  # 开发环境使用不同端口，避免与生产冲突
    volumes:
      - ./data/postgres-data-dev:/var/lib/postgresql/data  # 开发环境使用独立的数据目录
      - ./db/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    restart: always
    command: >
      bash -c "
        chmod +x /docker-entrypoint-initdb.d/init-db.sh &&
        docker-entrypoint.sh postgres
      "
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres-data-dev:
    driver: local

networks:
  default:
    driver: bridge
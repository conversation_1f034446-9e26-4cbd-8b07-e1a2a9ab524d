FROM python:3.13-slim

WORKDIR /app

# 复制依赖文件
COPY requirements.pip .
COPY agents/ ./agents/
COPY prompts/ ./prompts/
COPY state/ ./state/
COPY modules/ ./modules/

# 安装依赖项
RUN pip install --no-cache-dir -r requirements.pip -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制应用程序代码
COPY server.py migrate.py test_modules.py REFACTOR.md ./
COPY *.yaml *.json .env* ./

# 复制修复的 OpenAI 客户端
COPY bugs/_openai_client.py /usr/local/lib/python3.13/site-packages/autogen_ext/models/openai/_openai_client.py

# 暴露端口
EXPOSE 8000

# 配置环境变量
ENV PYTHONUNBUFFERED=1

# 启动应用
CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8000"]
"""
数据库连接和操作模块
"""
import asyncio
import asyncpg
import logging
import json
import os
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    before_sleep_log,
    retry_if_exception_type,
)
from typing import List, Dict
from loguru import logger
from autogen_agentchat.base import TaskResult


# 存储数据库连接池
db_pool = None

# 数据库配置
DATABASE_URL = os.environ.get(
    "DATABASE_URL", "postgresql://postgres:postgres@localhost:15435/aidb"
)


@retry(
    stop=stop_after_attempt(10),  # 增加尝试次数到10次
    wait=wait_exponential(
        multiplier=1, min=4, max=60
    ),  # 加大退避时间：4、8、16、32、60秒
    before_sleep=before_sleep_log(logger, logging.INFO),  # 每次重试前记录日志
    retry=retry_if_exception_type(
        (asyncpg.exceptions.PostgresError, ConnectionError, OSError)
    ),  # 显式添加OSError
)
async def create_db_pool(database_url):
    print(f"尝试连接数据库: {database_url}")
    try:
        # 添加连接超时参数
        return await asyncpg.create_pool(
            database_url, command_timeout=60.0, timeout=10.0
        )
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        logger.error(f"数据库连接异常: {type(e).__name__}: {str(e)}")
        raise  # 重新抛出异常以触发重试


# 记录对话到数据库
# async def save_conversation(session_id: str, message: TextMessage):
#     """将对话消息保存到数据库中"""
#     global db_pool
#     if not db_pool:
#         db_pool = await create_db_pool(DATABASE_URL)

#     async with db_pool.acquire() as conn:
#         await conn.execute(
#             "INSERT INTO conversation_records (session_id, role, content, timestamp, prompt_tokens, completion_tokens) VALUES ($1, $2, $3, $4, $5, $6)",
#             session_id,
#             message.source,
#             message.to_text(),
#             datetime.utcnow(),
#             # 先检查属性是否存在
#             message.models_usage.prompt_tokens if hasattr(message, 'models_usage') and message.models_usage else None,
#             message.models_usage.completion_tokens if hasattr(message, 'models_usage') and message.models_usage else None
#         )


# 记录用户对话到数据库
async def save_user_conversation(session_id: str, messages: List[Dict]):
    # 计算token使用量
    total_prompt_tokens = 0
    total_completion_tokens = 0

    message_list = []
    for message in messages:
        item_msg_prompt_tokens = 0
        item_msg_completion_tokens = 0

        # 累计token使用量
        if hasattr(message, 'models_usage') and message.models_usage:
            if hasattr(message.models_usage, 'prompt_tokens') and message.models_usage.prompt_tokens:
                item_msg_prompt_tokens = message.models_usage.prompt_tokens
                total_prompt_tokens += item_msg_prompt_tokens
            if hasattr(message.models_usage, 'completion_tokens') and message.models_usage.completion_tokens:
                item_msg_completion_tokens = message.models_usage.completion_tokens
                total_completion_tokens += item_msg_completion_tokens

        # 将消息转换为JSON格式
        message_list.append({
            "source": message["source"],
            "content": message["content"],
            "prompt_tokens": item_msg_prompt_tokens,
            "completion_tokens": item_msg_completion_tokens,
            "type": message["type"] if "type" in message else None,
        })

    logger.info("HISTORY: {}".format(message_list))

    message_list_str = json.dumps(message_list)

    global db_pool
    if not db_pool:
        db_pool = await create_db_pool(DATABASE_URL)

    async with db_pool.acquire() as conn:
        # 先检查是否已有对应的记录
        existing_record = await conn.fetchrow(
            "SELECT id FROM conversation_records WHERE session_id = $1 AND user_id = $2",
            session_id,
            "test"  # 假设user_id为"test"，实际应用中应替换为真实的用户ID
        )
        
        if existing_record:
            # 如果已有记录，则更新
            await conn.execute(
                "UPDATE conversation_records SET messages = $1, prompt_tokens = $2, completion_tokens = $3, timestamp = CURRENT_TIMESTAMP WHERE session_id = $4 AND user_id = $5",
                message_list_str,
                total_prompt_tokens,
                total_completion_tokens,
                session_id,
                "test"
            )
            logger.info(f"Updated conversation record for session_id: {session_id}")
        else:
            # 如果没有记录，则新建
            await conn.execute(
                "INSERT INTO conversation_records (session_id, user_id, messages, prompt_tokens, completion_tokens) VALUES ($1, $2, $3, $4, $5)",
                session_id,
                "test",  # 假设user_id为"test"，实际应用中应替换为真实的用户ID
                message_list_str,
                total_prompt_tokens,
                total_completion_tokens
            )
            logger.info(f"Created new conversation record for session_id: {session_id}")


async def get_conversation_history(session_id: str):
    """获取指定会话的所有对话历史记录，按时间顺序排列"""
    global db_pool
    if not db_pool:
        db_pool = await create_db_pool(DATABASE_URL)

    async with db_pool.acquire() as conn:
        record = await conn.fetch(
            """
            SELECT user_id, messages, timestamp, prompt_tokens, completion_tokens
            FROM conversation_records
            WHERE session_id = $1
            ORDER BY timestamp ASC
            """,
            session_id,
        )

        if not record:
            return []

        messages = json.loads(record[0]["messages"])

        logger.info("History: {}".format(messages))

        history = []
        # 检查messages是否有内容
        for msg in messages:
            if "type" in msg and msg["type"] in [
                "ThoughtEvent",
                "ToolCallRequestEvent",
                "ToolCallExecutionEvent",
                "ToolCallSummaryMessage",
            ]:
                continue

            if "content" not in msg or not msg["content"]:
                continue

            history.append({
                "role": "user" if msg["source"] == "user" else "aily",
                "content": msg["content"]
            })

        return history


async def startup_db():
    """启动时初始化数据库连接池"""
    global db_pool
    # 添加初始延迟，等待数据库服务完全启动
    print("等待5秒以确保数据库服务已启动...")
    await asyncio.sleep(5)
    db_pool = await create_db_pool(DATABASE_URL)
    print("数据库连接成功!")


async def shutdown_db():
    """关闭数据库连接池"""
    global db_pool
    if db_pool:
        await db_pool.close()
    print("数据库连接池已关闭")

"""
迁移脚本：从单文件版本迁移到模块化版本
"""
import os
import shutil
import sys

def migrate():
    """
    执行迁移操作，备份旧文件并替换为新文件
    """
    print("开始迁移...")
    
    # 检查新版服务器文件是否存在
    if not os.path.exists("server_new.py"):
        print("错误：找不到新版服务器文件 server_new.py")
        return False
    
    # 检查modules目录是否存在
    if not os.path.exists("modules"):
        print("错误：找不到modules目录")
        return False
    
    # 备份原始服务器文件
    if os.path.exists("server.py"):
        backup_file = "server.py.bak"
        i = 1
        while os.path.exists(backup_file):
            backup_file = f"server.py.bak.{i}"
            i += 1
        
        try:
            shutil.copy2("server.py", backup_file)
            print(f"已备份原始服务器文件到: {backup_file}")
        except Exception as e:
            print(f"备份失败: {str(e)}")
            return False
    
    # 复制新服务器文件
    try:
        shutil.copy2("server_new.py", "server.py")
        print("已复制新版服务器文件")
    except Exception as e:
        print(f"复制新服务器文件失败: {str(e)}")
        return False
    
    print("迁移完成！系统已成功更新到模块化版本。")
    print("您可以删除server_new.py文件，或者保留它作为备份。")
    return True


if __name__ == "__main__":
    if migrate():
        sys.exit(0)
    else:
        print("迁移失败，请手动检查并解决问题。")
        sys.exit(1)

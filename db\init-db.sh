#!/bin/bash
set -e

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE DATABASE userdb;
    CREATE DATABASE aidb;
    CREATE DATABASE kbdb;
EOSQL

# 连接到 aidb 数据库并创建表
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "aidb" <<-EOSQL
    CREATE TABLE IF NOT EXISTS conversation_records (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        session_id VARCHAR,
        user_id VARCHAR,
        messages TEXT,
        timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        prompt_tokens INTEGER DEFAULT 0,
        completion_tokens INTEGER DEFAULT 0,
        deleted BOOLEAN DEFAULT FALSE
    );
    
    -- 为 user_id 字段创建索引
    CREATE INDEX IF NOT EXISTS idx_conversation_records_user_id ON conversation_records (user_id);
EOSQL
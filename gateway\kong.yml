_format_version: "2.1"
_transform: true

services:
  # Kong API网关
  kong-database:
    image: postgres:14-alpine
    environment:
      - POSTGRES_USER=kong
      - POSTGRES_DB=kong
      - POSTGRES_PASSWORD=kongpass
    volumes:
      - kong-db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "kong"]
      interval: 5s
      timeout: 5s
      retries: 5

  kong-migrations:
    image: kong:3.0
    environment:
      - KONG_DATABASE=postgres
      - KONG_PG_HOST=kong-database
      - KONG_PG_USER=kong
      - KONG_PG_PASSWORD=kongpass
    command: kong migrations bootstrap
    depends_on:
      kong-database:
        condition: service_healthy
    restart: on-failure

  kong:
    image: kong:3.0
    environment:
      - KONG_DATABASE=postgres
      - KONG_PG_HOST=kong-database
      - KONG_PG_USER=kong
      - KONG_PG_PASSWORD=kongpass
      - KONG_PROXY_ACCESS_LOG=/dev/stdout
      - KONG_ADMIN_ACCESS_LOG=/dev/stdout
      - KONG_PROXY_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_LISTEN=0.0.0.0:8001
      - KONG_ADMIN_GUI_URL=http://localhost:8002
    depends_on:
      kong-migrations:
        condition: service_completed_successfully
    ports:
      - "8000:8000" # 代理
      - "8443:8443" # 代理 (HTTPS)
      - "8001:8001" # 管理API
      - "8444:8444" # 管理API (HTTPS)
      - "8002:8002" # 管理界面
    restart: on-failure
  # 用户服务
  # - name: user-service
  #   url: http://user-service:8000
  #   routes:
  #     - name: user-routes
  #       paths:
  #         - /api/users
  #       strip_path: false
  #   plugins:
  #     - name: jwt
  #     - name: rate-limiting
  #       config:
  #         second: 5
  #         hour: 1000
  #     - name: cors
      
  # AI系统服务
  - name: ai-service
    url: http://ai-service:8000
    routes:
      - name: ai-routes
        paths:
          - /api/conversations
          - /api/agents
        strip_path: false
    plugins:
      - name: jwt
      - name: rate-limiting
        config:
          second: 1
          hour: 500
          
  # 知识库服务
  # - name: kb-service
  #   url: http://kb-service:8000
  #   routes:
  #     - name: kb-routes
  #       paths:
  #         - /api/knowledge-bases
  #       strip_path: false
  #   plugins:
  #     - name: jwt
  #     - name: rate-limiting
  #       config:
  #         second: 3
  #         hour: 1000

consumers:
  - username: ai-system-client
    jwt_secrets:
      - key: "client-key"
        secret: "client-secret"
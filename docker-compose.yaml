services:
  ai-service:
    build: ./ai
    image: aily/ai-service:prod
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8000:8000"
    restart: unless-stopped
    # 生产环境不建议使用本地代码挂载，而是使用构建的镜像
    # volumes:
    #   - ./ai:/app
    volumes:
      - ./ai/logs:/app/logs
      - ./ai/state:/app/state
      - ./ai/prompts:/app/prompts
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=postgres
      - POSTGRES_MULTIPLE_DATABASES=userdb,aidb,kbdb
    ports:
      - "15432:5432"
    volumes:
      - ./data/postgres-data:/var/lib/postgresql/data
      - ./db/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    restart: unless-stopped
    command: >
      bash -c "
        chmod +x /docker-entrypoint-initdb.d/init-db.sh &&
        docker-entrypoint.sh postgres
      "
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres-data:
    driver: local

# 生产环境配置
networks:
  default:
    driver: bridge
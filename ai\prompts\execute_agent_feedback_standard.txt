# Execute Agent 标准反馈格式

所有execute agent完成任务后，必须使用统一的反馈格式向planner agent汇报执行结果。这确保planner能够获得足够的信息来规划后续步骤。

## 标准反馈模板

```
## 📋 执行摘要
**任务类型**: [简要描述执行的任务类型]
**执行状态**: ✅ 成功完成 / ⚠️ 部分完成 / ❌ 执行失败
**耗时**: [可选：执行用时]

## 📁 生成的核心文件/资源
**主要输出**:
- `[文件路径1]` - [文件描述和用途]
- `[文件路径2]` - [文件描述和用途]
- `[目录路径]` - [目录内容说明]

**相关配置**:
- [配置项1]: [配置值和说明]
- [配置项2]: [配置值和说明]

## 🔧 后续需要处理的任务
- [ ] [需要其他agent执行的相关任务]
- [ ] [需要验证或测试的内容]
- [ ] [可能的扩展或优化工作]

## ⚠️ 问题和注意事项
- [遇到的问题和解决方案]
- [需要特别注意的事项]
- [可能的风险点]

## 📝 重要上下文信息
[为后续agent提供的关键信息，包括：]
- 文件依赖关系
- 特殊配置要求
- 环境限制
- 兼容性注意事项
```

## 各类型Agent的特定要求

### Blockly Generation Agent
- 必须提供生成的所有Blockly相关文件（block.json, generator.js, toolbox.json, package.json等）
- 说明块的功能和使用方法
- 提供测试建议

### Project Creation Agent
- 提供项目路径和结构
- 列出已安装的库和依赖
- 说明项目配置

### Library Installation Agent
- 列出成功安装的库及版本
- 报告安装失败的库和原因
- 提供库的安装路径

### File Operation Agent
- 详细说明文件操作的类型和范围
- 提供操作后的文件结构
- 报告任何文件冲突或错误

### Compilation Error Repair Agent
- 说明修复的错误类型
- 提供修复前后的代码对比
- 确认编译状态

## 反馈质量要求

1. **准确性**: 所有路径和配置信息必须准确
2. **完整性**: 不遗漏任何重要的输出文件或配置
3. **可操作性**: 为后续步骤提供足够的上下文信息
4. **标准化**: 严格按照模板格式组织信息
5. **简洁性**: 避免冗余信息，突出重点

这种标准化的反馈格式确保了plan-and-execute模式中信息的有效传递和任务的连续性。

"""
Agent和团队管理模块
"""
import os
import json
import asyncio
import aiofiles
import re
from typing import List, Dict, Any, Optional
from autogen_core.models import UserMessage
from autogen_agentchat.base import TaskResult
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.models.anthropic import AnthropicChatCompletionClient
from autogen_agentchat.teams import SelectorGroupChat, RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination, ExternalTermination
from autogen_agentchat.messages import (
    TextMessage,
    BaseAgentEvent,
    BaseChatMessage,
    ToolCallRequestEvent,
    ToolCallExecutionEvent,
    ToolCallSummaryMessage,
)
from autogen_core import CancellationToken
from autogen_core.models import ModelInfo
from loguru import logger

from agents.aily_agent import AilyAgent
from modules.tools import FrontendTool, frontend_call
from modules.prompt import load_system_prompt_file
from modules.session import sessions, tool_result_queues, user_input_queues, response_queues

# 常量定义
AGENT_STATE_PATH = "state"

# Agent配置定义
AGENT_CONFIGS = {
    "plannerAgent": {
        "info": {
            "model": "gpt-4.1",
            "temperature": 0,
            "prompts": ["planner_agent.txt"],
            "description": "Planner Agent, 负责规划和协调任务。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="openai",
                structured_output=True,
            ),
        },
        "tool": {
            "all": False,
            "tools": ["ask_approval"],
        },
    },
    "projectAnalysisAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["project_analysis_agent.txt"],
            "description": "Project Analysis Agent, 专门负责需求分析阶段，深入分析和整理用户项目需求，为开发板和库推荐提供详细的技术基础。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "projectGenerationAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["project_generation_agent.txt"],
            "description": "Project Generation Agent, 负责项目构建，组合现有的各种库完成项目的构建",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "boardRecommendationAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["board_recommendation_agent.txt"],
            "description": "Board Recommendation Agent, 负责推荐合适的开发板。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "libraryRecommendationAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["library_recommendation_agent.txt"],
            "description": "Library Recommendation Agent, 负责推荐合适的库。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "arduinoLibraryAnalysisAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["arduino_library_analysis_agent.txt"],
            "description": "Arduino Library Analysis Agent, 专门负责分析Arduino库代码结构和功能，为转换为Blockly库做技术准备。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "projectCreationAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["project_creation_agent.txt"],
            "description": "Project Creation Agent, 负责创建新的项目。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "libraryInstallationAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["library_installation_agent.txt"],
            "description": "Library Installation Agent, 负责安装和管理库。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "blocklyGenerationAgent": {
        "info": {
            "model": "claude-sonnet-4-20250514",
            "temperature": 0,
            "prompts": ["blockly_generation_agent.txt"],
            "description": "Blockly Generation Agent, 负责生成Blockly代码。",
            "apikey": os.environ.get("ANTHROPIC_API_KEY"),
            "baseurl": os.environ.get("ANTHROPIC_BASE_URL"),
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="claude",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "blocklyRepairAgent": {
        "info": {
            "model": "qwen3-coder-plus",
            "temperature": 0,
            "prompts": ["blockly_repair_agent.txt"],
            "description": "Blockly Repair Agent, 负责修复Blockly代码。",
            "apikey": os.environ.get("QWEN_API_KEY"),
            "baseurl": os.environ.get("QWEN_BASE_URL"),
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="qwen",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "compilationErrorRepairAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["compilation_error_repair_agent.txt"],
            "description": "Compilation Error Repair Agent, 负责修复编译错误。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "fileOperationAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["file_operation_agent.txt"],
            "description": "File Operation Agent, 负责文件操作。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": True,
            "tools": [],
        },
    },
    "contextAgent": {
        "info": {
            "model": "gemini-2.5-pro",
            "temperature": 0,
            "prompts": ["context_agent.txt"],
            "description": "Context Agent, 用于获取上下文信息。",
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="gemini",
                structured_output=True,
            ),
        },
        "tool": {
            "all": False,
            "tools": ["get_context"],
        },
    },
}


def create_agent_from_config(
        agent_name: str,
        session_id: str, 
        front_tools: List = []
    ) -> AilyAgent:
    """根据配置创建agent的通用函数"""
    
    if agent_name not in AGENT_CONFIGS:
        raise ValueError(f"未知的agent类型: {agent_name}")
    
    agent_config = AGENT_CONFIGS[agent_name]
    config = agent_config["info"]
    tool_config = agent_config.get("tool", {})

    # 创建模型客户端
    model_client_kwargs = {
        "model": config["model"],
        "api_key": config.get("apikey") or os.environ.get("OPENAI_API_KEY"),
        "base_url": config.get("baseurl") or os.environ.get("OPENAI_BASE_URL")
    }
    
    if config["temperature"] is not None:
        model_client_kwargs["temperature"] = config["temperature"]
    
    if config["model_info"] is not None:
        model_client_kwargs["model_info"] = config["model_info"]

    if config["model_info"]["family"] == "claude":
        logger.info(f"使用Anthropic模型: {config['model']}")
        model_client = AnthropicChatCompletionClient(**model_client_kwargs)
    else:
        model_client = OpenAIChatCompletionClient(**model_client_kwargs)

    # 构建系统消息
    system_message_parts = []
    for prompt_file in config["prompts"]:
        system_message_parts.append(load_system_prompt_file(prompt_file))
    
    system_message = "\n\n".join(system_message_parts)
    
    # 确定使用的前端工具
    if tool_config.get("all", False):
        frontend_tools = front_tools
    else:
        # 只加载指定的tools
        config_tools = tool_config.get("tools", [])
        frontend_tools = [tool for tool in front_tools if tool.name in config_tools]
    
    return create_agent(
        name=agent_name,
        description=config["description"],
        session_id=session_id,
        model_client=model_client,
        default_tools=[],
        frontend_tools=frontend_tools,
        system_message=system_message
    )


def create_agent(
        name: str, 
        description: str,
        session_id: str, 
        model_client: OpenAIChatCompletionClient, 
        default_tools: List = [], 
        frontend_tools: List = [], 
        system_message: str = "You are helpful assistant"
    ) -> AilyAgent:
    """ 创建aily智能助手代理 
    """

    # 创建前端调用函数的闭包
    async def session_frontend_call(tool):
        return await frontend_call(tool, sessions, tool_result_queues, response_queues)

    # 创建闭包函数来捕获session_id
    async def session_aware_frontend_call(tool):
        # 确保 tool.id 不为 None，如果为 None 则使用 tool.name 作为备用
        tool_id = tool.id if tool.id is not None else tool.name
        
        try:
            # 标记当前正在处理的工具
            sessions[session_id]["current_tool"] = tool_id

            logger.info(f"{session_id}, 调用工具: {tool.name} (ID: {tool_id})")
            return await session_frontend_call(tool)
        finally:
            # 清理标记
            if "current_tool" in sessions[session_id]:
                del sessions[session_id]["current_tool"]

    agent_tools = []
    for tool in frontend_tools:
        frontend_tool = FrontendTool(
            name=f"{tool.name}",
            description=tool.description,
            input_schema=tool.input_schema,
            frontend_call=session_aware_frontend_call,
        )
        agent_tools.append(frontend_tool)

    agent_tools.extend(default_tools)
    
    aily_agent = AilyAgent(
        name=name,
        description=description,
        model_client=model_client,
        model_client_stream=True,
        system_message=system_message,
        tools=agent_tools,  # 这里使用转换后的工具
        frontend_call=session_aware_frontend_call,
        reflect_on_tool_use=False
    )

    return aily_agent

def create_user_agent(session_id: str) -> UserProxyAgent:
    """创建用户代理"""
    async def user_input_func(
        prompt: str, cancellation_token: CancellationToken | None
    ) -> str:
        """获取用户输入的函数 - 参考app_team.py的实现"""
        if session_id not in user_input_queues:
            user_input_queues[session_id] = asyncio.Queue()
        queue = user_input_queues[session_id]

        # 向前端发送请求用户输入的消息
        if session_id in response_queues:
            await response_queues[session_id].put(
                json.dumps(
                    {
                        "type": "user_input_required",
                        "prompt": prompt,  # 可能包含提示用户的文本
                    }
                )
            )
            logger.info(f"已发送用户输入请求到前端，会话ID: {session_id}, 提示: {prompt}")

        try:
            # 等待用户输入
            user_input = await asyncio.wait_for(queue.get(), timeout=360)
            logger.info(f"接收到用户输入: {user_input}")
            return user_input
        except asyncio.TimeoutError:
            logger.warning(f"用户输入超时，会话ID: {session_id}")
            # 往客户端发送超时消息
            if session_id in response_queues:
                await response_queues[session_id].put(
                    json.dumps(
                        {
                            "type": "error",
                            "message": "用户输入超时，对话已结束。",
                        }
                    )
                )
            return "TERMINATE"  # 返回终止指令
        except Exception as e:
            logger.error(f"获取用户输入时发生错误: {str(e)}")
            # 如果出现其他错误，默认返回TERMINATE
            return "TERMINATE"

    user_agent = UserProxyAgent(
        name="user",
        input_func=user_input_func,  # 使用队列来处理用户输入
    )

    return user_agent

# 便利函数：批量创建代理
def create_agents_batch(agent_names: List[str], session_id: str, front_tools: List = []) -> Dict[str, AilyAgent]:
    """批量创建多个代理"""
    agents = {}
    for agent_name in agent_names:
        if agent_name == "user":
            agents[agent_name] = create_user_agent(session_id)
        else:
            agents[agent_name] = create_agent_from_config(agent_name, session_id, front_tools)
    return agents

# 便利函数：获取所有可用的代理类型
def get_available_agent_types() -> List[str]:
    """获取所有可用的代理类型"""
    return list(AGENT_CONFIGS.keys()) + ["user"]

# 便利函数：添加新的代理配置
def register_agent_config(agent_name: str, config: Dict[str, Any]) -> None:
    """注册新的代理配置"""
    AGENT_CONFIGS[agent_name] = config

# 示例：如何添加新的代理类型
def example_register_new_agent():
    """示例：注册一个新的代理类型"""
    new_agent_config = {
        "info": {
            "model": "gpt-4o",
            "temperature": 0.5,
            "prompts": ["new_agent.txt", "tools_use.txt"],
            "description": "New Agent, 新的专用代理。",
            "has_frontend_tools": True,
            "apikey": None,
            "baseurl": None,
            "model_info": ModelInfo(
                vision=True,
                function_calling=True,
                json_output=True,
                family="openai",
                structured_output=True
            )
        },
        "tools": []
    }
    register_agent_config("new_agent", new_agent_config)
    # 使用方式：
    # new_agent = create_agent_from_config("new_agent", session_id, front_tools)

def candidate_func(messages: List[BaseAgentEvent | BaseChatMessage]) -> List[str]:
    """团队代理选择函数 - 基于Plan-and-Execute模式"""

    # 处理执行代理的直接用户交互请求
    execution_agents = [
        "blocklyGenerationAgent",
        "blocklyRepairAgent",
        "projectAnalysisAgent",
        "boardRecommendationAgent",
        "projectGenerationAgent",
        "libraryRecommendationAgent",
        "arduinoLibraryAnalysisAgent",
        "projectCreationAgent",
        "compilationErrorRepairAgent",
        "contextAgent",
        "libraryInstallationAgent",
        "fileOperationAgent",
    ]

    # 检查是否有TERMINATE指令
    if isinstance(messages[-1], TextMessage) and "TERMINATE" in messages[-1].content:
        logger.info("检测到TERMINATE指令，结束对话")
        return []

    # 处理工具调用结果 - 返回给调用工具的代理进行反馈处理
    if isinstance(messages[-1], (ToolCallSummaryMessage, ToolCallExecutionEvent)):
        if len(messages) > 1:
            # 工具调用完成后，返回给发起调用的agent
            logger.info(f"工具调用完成，返回给发起调用的agent: {messages[-2].source}")
            return [messages[-2].source]

    # 处理空消息
    if (messages[-1].source != 'user' and not messages[-1].content):
        return [messages[-1].source]

    # 处理文本消息
    if isinstance(messages[-1], TextMessage):
        content = messages[-1].content.lower()

        # 检查plannerAgent的任务分配指令
        if messages[-1].source == "plannerAgent":
            # 定义所有可能的代理分配指令，使用正则表达式来匹配
            # 定义代理映射，支持大小写变体
            agent_patterns = [
                (r"to_blocklygenerationagent|to_blocklyGenerationAgent", "blocklyGenerationAgent"),
                (r"to_blocklyrepairagent|to_blocklyRepairAgent", "blocklyRepairAgent"),
                (r"to_projectanalysisagent|to_projectAnalysisAgent", "projectAnalysisAgent"),
                (r"to_projectgenerationagent|to_projectGenerationAgent", "projectGenerationAgent"),
                (r"to_boardrecommendationagent|to_boardRecommendationAgent", "boardRecommendationAgent"),
                (r"to_libraryrecommendationagent|to_libraryRecommendationAgent", "libraryRecommendationAgent"),
                (r"to_arduinolibraryanalysisagent|to_arduinoLibraryAnalysisAgent", "arduinoLibraryAnalysisAgent"),
                (r"to_projectcreationagent|to_projectCreationAgent", "projectCreationAgent"),
                (r"to_compilationerrorrepairagent|to_compilationErrorRepairAgent", "compilationErrorRepairAgent"),
                (r"to_libraryinstallationagent|to_libraryInstallationAgent", "libraryInstallationAgent"),
                (r"to_contextagent|to_contextAgent", "contextAgent"),
                (r"to_fileoperationagent|to_fileOperationAgent", "fileOperationAgent"),
                (r"to_user|to_User", "user")
            ]

            # 找到所有匹配的指令及其位置
            last_match = None
            last_position = -1

            for pattern, agent_name in agent_patterns:
                matches = list(re.finditer(pattern, content))
                if matches:
                    # 获取最后一个匹配项的位置
                    last_match_in_pattern = matches[-1]
                    if last_match_in_pattern.end() > last_position:
                        last_position = last_match_in_pattern.end()
                        last_match = agent_name

            logger.info(f"plannerAgent指令解析Agent: {last_match}")

            if last_match:
                if last_match == "user":
                    logger.info("planner指示转交给用户")
                elif last_match == "projectCreationAgent":
                    logger.info("planner分配任务给项目代理")
                else:
                    logger.info(f"planner分配任务给{last_match}代理")
                return [last_match]
            else:
                # planner没有明确指派，返回所有执行代理
                logger.info("planner没有明确指派, 返回所有执行代理")
                return execution_agents

        if messages[-1].source in execution_agents:
            # 检查是否包含直接用户交互指令
            if any(keyword in content for keyword in ["need_user_confirm", "ask_user", "user_input_required", "confirm_with_user", "to_user"]):
                logger.info(f"{messages[-1].source}请求直接用户交互")
                return ["user"]
            # 检查是否是任务完成报告，返回给planner进行下一步规划
            elif any(keyword in content for keyword in ["task_completed", "finished", "done", "completed"]):
                logger.info(f"{messages[-1].source}任务完成，返回给plannerAgent进行下一步规划") 
                return ["plannerAgent"]
            # 默认情况：执行完成，返回给planner
            else:
                logger.info(f"{messages[-1].source}执行完成，返回给plannerAgent进行下一步规划")
                return ["plannerAgent"]

    # 处理用户消息 - 优先交给plannerAgent进行规划
    if messages[-1].source == "user":
        # 检查是否是对话的开始或用户的新需求
        if len(messages) == 1 or (len(messages) > 1 and messages[-2].source == "user"):
            logger.info("用户新需求，转交给plannerAgent进行规划")
            return ["plannerAgent"]
        elif len(messages) > 1:
            # 如果前一条消息来自执行代理，且用户提供了反馈
            prev_source = messages[-2].source
            execution_agents = [
                "blocklyGenerationAgent",
                "blocklyRepairAgent",
                "projectAnalysisAgent",
                "projectGenerationAgent",
                "boardRecommendationAgent",
                "libraryRecommendationAgent",
                "arduinoLibraryAnalysisAgent",
                "projectCreationAgent",
                "compilationErrorRepairAgent",
                "contextAgent",
                "libraryInstallationAgent",
                "fileOperationAgent",
            ]

            if prev_source in execution_agents:
                # 检查用户回复是否是简单确认/否定，如果是则直接返回给执行代理
                simple_responses = ["yes", "no", "ok", "确认", "取消", "同意", "不同意", "好的", "可以"]
                if any(response in content.lower() for response in simple_responses):
                    logger.info(f"用户简单确认回复，直接返回给执行代理: {prev_source}")
                    return [prev_source]
                # 复杂反馈交给planner调整计划
                else:
                    logger.info("用户对执行结果提供复杂反馈，转交给plannerAgent调整计划")
                    return ["plannerAgent"]
            elif prev_source != "user":
                logger.info("继续与上一个代理对话")
                return [prev_source]

    # 默认情况：交给plannerAgent进行统一规划和调度
    logger.info("默认情况：交给plannerAgent进行规划")
    return ["plannerAgent"]

async def get_team(session_id: str, tools: List[any], external_termination: ExternalTermination) -> SelectorGroupChat:
    """获取或创建一个agent团队 - 基于Plan-and-Execute模式"""

    # 使用批量创建代理
    agent_names = [
        "plannerAgent",
        "projectAnalysisAgent",
        "projectGenerationAgent",
        "boardRecommendationAgent",
        "libraryRecommendationAgent",
        "arduinoLibraryAnalysisAgent",
        "projectCreationAgent",
        "blocklyGenerationAgent",
        "blocklyRepairAgent",
        "compilationErrorRepairAgent",
        "contextAgent",
        "libraryInstallationAgent",
        "fileOperationAgent",
    ]
    agents = create_agents_batch(agent_names, session_id, tools)

    # 提取代理实例
    participants = [agent.name for agent in agents.values()]
    agent_instances = list(agents.values())

    user = create_user_agent(session_id)
    agent_instances.append(user)

    text_mention_termination = TextMentionTermination("TERMINATE")
    max_message_termination = MaxMessageTermination(max_messages=100)  # 增加最大消息数以支持迭代模式

    termination = text_mention_termination | max_message_termination | external_termination

    selector_prompt = """Select an agent to perform the next task based on the Plan-and-Execute methodology.
        {roles}

        Current conversation context:
        {history}

        AGENT SELECTION RULES:
        1. For new user requests: Always select "plannerAgent" first to analyze and create execution plan
        2. When plannerAgent assigns tasks: Select the specific agent mentioned in the planner's response
        3. When execution agents complete tasks: Return control to "plannerAgent" for next step planning
        4. When user provides feedback: Route back to "plannerAgent" for plan adjustment
        5. For user input requests: Select "user" agent

        WORKFLOW:
        - plannerAgent: Analyzes requests, creates plans, assigns tasks, adjusts plans based on feedback
        - projectAnalysisAgent: Analyzes project requirements, provides suggestions
        - projectGenerationAgent: Combines libraries to create projects
        - boardRecommendationAgent: Recommends suitable development boards
        - libraryRecommendationAgent: Recommends suitable libraries
        - arduinoLibraryAnalysisAgent: Analyzes Arduino library code structure for Blockly conversion
        - projectCreationAgent: Creates new projects
        - blocklyGenerationAgent: Generates Blockly code
        - blocklyRepairAgent: Repairs Blockly code
        - compilationErrorRepairAgent: Repairs compilation errors
        - user: Handles user input and feedback
        - contextAgent: Handles context-related tasks
        - libraryInstallationAgent: Installs required libraries
        - fileOperationAgent: Handles file operations

        Select from {participants}. Only select ONE agent that should handle the next step.
    """

    select_model_client = OpenAIChatCompletionClient(
        model="gpt-4.1",
        api_key=os.environ.get("OPENAI_API_KEY"),
        base_url=os.environ.get("OPENAI_BASE_URL"),
        temperature=0,
        model_info=ModelInfo(
            vision=False,
            function_calling=True,
            json_output=True,
            family="openai",
            structured_output=True
        )
    )

    team = SelectorGroupChat(
        agent_instances,
        model_client=select_model_client,
        termination_condition=termination,
        selector_prompt=selector_prompt,
        allow_repeated_speaker=True,
        candidate_func=candidate_func,
    )

    state_name = f"state_{session_id}.json"
    state_path = os.path.join(AGENT_STATE_PATH, state_name)
    if not os.path.exists(state_path):
        return team

    try:
        async with aiofiles.open(state_path, "r") as file:
            state = json.loads(await file.read())
    except Exception as e:
        print(f"读取团队状态失败: {str(e)}")
        # 如果读取失败，返回未加载状态的团队
        return team

    # 尝试加载状态到团队
    try:
        await team.load_state(state)
    except Exception as e:
        print(f"加载团队状态失败: {str(e)}")
        # 如果加载失败，返回未加载状态的团队
    return team

async def save_team_state(session_id: str, team):
    """保存团队状态到文件"""
    state_name = f"state_{session_id}.json"
    state_path = os.path.join(AGENT_STATE_PATH, state_name)
    os.makedirs(AGENT_STATE_PATH, exist_ok=True)
    async with aiofiles.open(state_path, "w") as file:
        state = await team.save_state()
        await file.write(json.dumps(state, default=str))

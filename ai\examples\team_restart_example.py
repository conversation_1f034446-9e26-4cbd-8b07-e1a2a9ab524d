#!/usr/bin/env python3
"""
团队重启功能示例

展示如何在团队运行失败后重新启动团队的各种方法。
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

class TeamRestartManager:
    """团队重启管理器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        
    async def reset_team(self, session_id: str) -> Dict[str, Any]:
        """重置团队到初始状态
        
        这个方法调用Team.reset()来重置团队和所有参与者到初始状态。
        适用于：
        - 团队状态混乱但结构完整
        - 需要清除对话历史重新开始
        - 轻量级的重启需求
        """
        async with aiohttp.ClientSession() as session:
            url = f"{self.base_url}/api/v1/reset_team/{session_id}"
            async with session.post(url) as response:
                return await response.json()
    
    async def restart_team(self, session_id: str, tools: list = None) -> Dict[str, Any]:
        """完全重启团队
        
        这个方法会：
        1. 首先尝试重置现有团队
        2. 如果重置失败，重新创建团队
        3. 保存新的团队状态
        
        适用于：
        - 团队出现严重错误
        - 需要完全重新初始化
        - 重置失败的情况
        """
        async with aiohttp.ClientSession() as session:
            url = f"{self.base_url}/api/v1/restart_team/{session_id}"
            payload = {
                "session_id": session_id,
                "tools": tools or []
            }
            async with session.post(url, json=payload) as response:
                return await response.json()
    
    async def check_team_status(self, session_id: str) -> bool:
        """检查团队是否存在并正常运行"""
        async with aiohttp.ClientSession() as session:
            url = f"{self.base_url}/api/v1/conversation_history/{session_id}"
            async with session.get(url) as response:
                result = await response.json()
                return result.get("status") == "success"

async def demonstrate_team_restart():
    """演示团队重启功能"""
    manager = TeamRestartManager()
    
    # 假设的会话ID
    session_id = "example-session-123"
    
    print("=== 团队重启功能演示 ===")
    
    # 1. 检查团队状态
    print(f"\n1. 检查团队 {session_id} 状态...")
    is_active = await manager.check_team_status(session_id)
    print(f"团队状态: {'活跃' if is_active else '不存在或异常'}")
    
    # 2. 演示轻量级重置
    print(f"\n2. 执行轻量级团队重置...")
    reset_result = await manager.reset_team(session_id)
    print(f"重置结果: {reset_result}")
    
    # 3. 演示完全重启
    print(f"\n3. 执行完全团队重启...")
    restart_result = await manager.restart_team(session_id, tools=[])
    print(f"重启结果: {restart_result}")

def demonstrate_error_patterns():
    """演示不同的错误处理模式"""
    
    print("\n=== 错误处理模式说明 ===")
    
    error_patterns = {
        "需要重启的错误类型": [
            "agent container 相关错误",
            "timeout 超时错误", 
            "connection 连接错误",
            "memory 内存相关错误",
            "state 状态相关错误",
            "corrupted 数据损坏",
            "deadlock 死锁"
        ],
        "异常类型触发重启": [
            "RuntimeError",
            "ConnectionError", 
            "TimeoutError",
            "MemoryError"
        ]
    }
    
    for category, patterns in error_patterns.items():
        print(f"\n{category}:")
        for pattern in patterns:
            print(f"  - {pattern}")

def usage_guide():
    """使用指南"""
    
    print("\n=== 使用指南 ===")
    
    scenarios = {
        "场景1：对话过程中出现错误": {
            "描述": "团队在处理消息时遇到错误，会自动判断是否需要重启",
            "处理方式": "系统自动检测错误类型，符合条件则自动重启团队",
            "用户操作": "无需操作，系统会通知重启状态"
        },
        
        "场景2：手动重置团队": {
            "描述": "用户感觉团队状态异常，希望重新开始",
            "处理方式": "调用 /api/v1/reset_team/{session_id} API",
            "用户操作": "发送POST请求到重置端点"
        },
        
        "场景3：完全重启团队": {
            "描述": "团队出现严重问题，需要完全重新创建",
            "处理方式": "调用 /api/v1/restart_team/{session_id} API", 
            "用户操作": "发送POST请求到重启端点，包含工具配置"
        }
    }
    
    for scenario, details in scenarios.items():
        print(f"\n{scenario}:")
        for key, value in details.items():
            print(f"  {key}: {value}")

def api_reference():
    """API参考"""
    
    print("\n=== API 参考 ===")
    
    apis = {
        "POST /api/v1/reset_team/{session_id}": {
            "功能": "重置团队到初始状态",
            "参数": "session_id (路径参数)",
            "返回": '{"status": "success/error", "message": "..."}'
        },
        
        "POST /api/v1/restart_team/{session_id}": {
            "功能": "完全重启团队",
            "参数": "session_id (路径参数) + SessionRequest (请求体)",
            "请求体": '{"session_id": "...", "tools": [...]}',
            "返回": '{"status": "success/error", "message": "..."}'
        }
    }
    
    for endpoint, details in apis.items():
        print(f"\n{endpoint}:")
        for key, value in details.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    print("Aily Services - 团队重启功能使用指南")
    
    # 运行演示
    asyncio.run(demonstrate_team_restart())
    
    # 显示错误处理模式
    demonstrate_error_patterns()
    
    # 显示使用指南
    usage_guide()
    
    # 显示API参考
    api_reference()

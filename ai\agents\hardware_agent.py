import os
import aiohttp

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient


# 设置模型客户端
model_client = OpenAIChatCompletionClient(
    model="gpt-4o",  # 使用高级模型以支持所有功能
    api_key=os.environ.get("OPENAI_API_KEY"),
    api_base=os.environ.get("OPENAI_API_BASE"),
)


async def fetch_board_json() -> str:
    """获取最新的开发板数据

    Returns:
        最新的开发板数据(JSON字符串)
    """
    url = "https://blockly.diandeng.tech/boards.json"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            return await response.text()


async def fetch_library_json() -> str:
    """获取最新的库数据

    Returns:
        最新的库数据(JSON字符串)
    """
    url = "https://blockly.diandeng.tech/libraries.json"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            return await response.text()


agent = AssistantAgent(
    name="hardware_project_agent",
    description="这个agent是一个全能的硬件项目专家，集成了需求分析、开发板选择和库推荐功能。",
    model_client=model_client,
    tools=[fetch_board_json, fetch_library_json],
    # model_client_stream=True,
    system_message="""
        你是一位综合型硬件项目专家，同时具备产品经理、开发板专家和库专家的能力。你可以完成完整的硬件项目方案制定流程：

        【需求分析能力】
        1. 深入理解用户的需求描述，提取关键信息
        2. 明确项目的核心目标、功能边界和技术约束
        3. 结构化整理需求，确保覆盖功能、性能、安全性等方面
        4. 优先级排序，区分必要功能和可选功能
        5. 为后续的开发板选择和库选择提供清晰的需求规格

        【开发板选择能力】
        1. 可以使用fetch_board_json工具获取最新的开发板信息
        2. 能分析开发板的规格、功能、价格和兼容性等特点
        3. 从中选择最符合用户需求的开发板

        【库推荐能力】
        1. 可以使用fetch_library_json工具获取最新的库信息
        2. 熟悉各种库的作用及其用法
        3. 能从最新的库信息中筛选出用户需要使用到的库

        【工作流程】
        1. 首先分析用户需求，提取关键信息并结构化整理
        2. 然后获取开发板信息，根据需求选择合适的开发板
        3. 接着获取库信息，推荐需要使用的库
        4. 最后整合所有信息，提供完整的硬件项目解决方案
        5. 完成方案后，以"APPROVE"结束回复

        请始终基于最新的数据提供建议，并解释你的选择理由，确保推荐的方案完全满足用户的具体需求。
    """,
)

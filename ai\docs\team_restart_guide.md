# 团队重启功能详细说明

## 概述

基于AutoGen的`Team.reset()`方法，我们为Aily Services实现了完整的团队重启功能。当团队运行失败或出现异常时，系统可以自动或手动重启团队，确保服务的稳定性和可用性。

## 核心功能

### 1. 团队重置 (Team Reset)

**功能说明：**
- 调用AutoGen的`Team.reset()`方法
- 将团队和所有参与者重置到初始状态
- 清除对话历史和中间状态
- 保持团队结构不变

**使用场景：**
- 团队状态混乱但结构完整
- 需要清除对话历史重新开始
- 轻量级的重启需求

**API端点：**
```http
POST /api/v1/reset_team/{session_id}
```

### 2. 团队重启 (Team Restart)

**功能说明：**
- 首先尝试重置现有团队
- 如果重置失败，重新创建团队
- 保存新的团队状态
- 通知前端重启状态

**使用场景：**
- 团队出现严重错误
- 需要完全重新初始化
- 重置失败的情况

**API端点：**
```http
POST /api/v1/restart_team/{session_id}
```

**请求体：**
```json
{
  "session_id": "session_id",
  "tools": []
}
```

### 3. 自动错误恢复

**功能说明：**
- 在消息处理过程中自动检测错误
- 根据错误类型判断是否需要重启
- 自动执行重启流程
- 通知用户重启状态

**触发条件：**

#### 错误消息模式：
- `agent container` - agent容器相关错误
- `timeout` - 超时错误
- `connection` - 连接错误
- `memory` - 内存相关错误
- `state` - 状态相关错误
- `corrupted` - 数据损坏
- `deadlock` - 死锁

#### 异常类型：
- `RuntimeError`
- `ConnectionError`
- `TimeoutError`
- `MemoryError`

## 实现细节

### 核心函数

#### 1. `reset_team(session_id: str) -> bool`

```python
async def reset_team(session_id: str) -> bool:
    """重置团队到初始状态"""
    try:
        if session_id in sessions and "team" in sessions[session_id]:
            team = sessions[session_id]["team"]
            await team.reset()  # 调用AutoGen的reset方法
            logger.info(f"团队 {session_id} 已成功重置到初始状态")
            return True
        else:
            logger.warning(f"会话 {session_id} 不存在或没有团队实例")
            return False
    except Exception as e:
        logger.error(f"重置团队 {session_id} 失败: {e}", exc_info=True)
        return False
```

#### 2. `restart_team_after_failure(session_id, tools, external_termination) -> bool`

```python
async def restart_team_after_failure(session_id: str, tools: list, external_termination) -> bool:
    """团队运行失败后的完整重启流程"""
    try:
        # 首先尝试重置现有团队
        reset_success = await reset_team(session_id)
        
        if not reset_success:
            # 如果重置失败，重新创建团队
            from modules.agents import get_team
            new_team = await get_team(session_id, tools, external_termination)
            sessions[session_id]["team"] = new_team
            logger.info(f"为会话 {session_id} 成功创建新团队")
        
        # 保存团队状态
        team = sessions[session_id]["team"]
        from modules.agents import save_team_state
        await save_team_state(session_id, team)
        
        # 通知前端团队已重启
        if session_id in response_queues:
            await response_queues[session_id].put(
                json.dumps({
                    "type": "team_restarted", 
                    "message": "团队已重新启动，可以继续对话"
                })
            )
        
        return True
        
    except Exception as e:
        logger.error(f"重启团队 {session_id} 失败: {e}", exc_info=True)
        return False
```

#### 3. `should_restart_team_on_error(error: Exception) -> bool`

```python
async def should_restart_team_on_error(error: Exception) -> bool:
    """判断是否应该基于错误类型重启团队"""
    error_message = str(error)
    
    # 定义需要重启的错误模式
    restart_patterns = [
        "agent container", "timeout", "connection", 
        "memory", "state", "corrupted", "deadlock"
    ]
    
    # 检查错误消息是否匹配需要重启的模式
    for pattern in restart_patterns:
        if pattern.lower() in error_message.lower():
            return True
    
    # 检查特定的异常类型
    restart_exception_types = [
        "RuntimeError", "ConnectionError", "TimeoutError", "MemoryError"
    ]
    
    for exc_type in restart_exception_types:
        if exc_type in str(type(error)):
            return True
    
    return False
```

### 消息处理增强

在`process_message`函数中添加了错误恢复机制：

```python
except Exception as e:
    error_message = str(e)
    logger.error("处理消息时出错: {}", error_message, exc_info=True)
    
    # 检查是否需要重启团队
    should_restart = await should_restart_team_on_error(e)
    
    if should_restart:
        logger.info(f"检测到严重错误，尝试重启团队 {session_id}")
        
        # 通知前端正在重启团队
        if session_id in response_queues:
            response_queues[session_id].put_nowait(
                json.dumps({
                    "type": "team_restarting", 
                    "message": "检测到错误，正在重启团队..."
                })
            )
        
        # 尝试重启团队
        restart_success = await restart_team_after_failure(
            session_id, tools, external_termination
        )
        
        if restart_success:
            error_message += " 团队已自动重启，您可以重新发送消息。"
        else:
            error_message += " 团队重启失败，请尝试重新开始会话。"
```

## 前端通知机制

系统会向前端发送以下类型的通知：

### 1. 团队重启中
```json
{
  "type": "team_restarting",
  "message": "检测到错误，正在重启团队..."
}
```

### 2. 团队重启成功
```json
{
  "type": "team_restarted",
  "message": "团队已重新启动，可以继续对话"
}
```

### 3. 团队重启失败
```json
{
  "type": "team_restart_failed",
  "message": "团队重启失败: 具体错误信息"
}
```

## 使用示例

### Python客户端示例

```python
import aiohttp
import asyncio

async def restart_team_example():
    session_id = "your-session-id"
    
    # 1. 重置团队
    async with aiohttp.ClientSession() as session:
        url = f"http://localhost:8000/api/v1/reset_team/{session_id}"
        async with session.post(url) as response:
            result = await response.json()
            print(f"重置结果: {result}")
    
    # 2. 完全重启团队
    async with aiohttp.ClientSession() as session:
        url = f"http://localhost:8000/api/v1/restart_team/{session_id}"
        payload = {
            "session_id": session_id,
            "tools": []
        }
        async with session.post(url, json=payload) as response:
            result = await response.json()
            print(f"重启结果: {result}")

asyncio.run(restart_team_example())
```

### curl命令示例

```bash
# 重置团队
curl -X POST http://localhost:8000/api/v1/reset_team/your-session-id

# 重启团队
curl -X POST http://localhost:8000/api/v1/restart_team/your-session-id \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your-session-id", "tools": []}'
```

## 监控和日志

### 日志级别
- `INFO`: 正常的重启操作
- `WARNING`: 会话不存在或其他警告
- `ERROR`: 重启失败的详细错误信息

### 日志示例
```
2025-07-30 10:30:45 | INFO | 团队 session-123 已成功重置到初始状态
2025-07-30 10:31:20 | INFO | 检测到严重错误，尝试重启团队 session-123
2025-07-30 10:31:22 | INFO | 为会话 session-123 成功创建新团队
2025-07-30 10:31:23 | ERROR | 重启团队 session-456 失败: Connection timeout
```

## 最佳实践

### 1. 预防性重启
- 定期检查团队状态
- 在长时间运行后主动重置
- 监控内存使用情况

### 2. 错误处理策略
- 优先尝试轻量级重置
- 只在必要时进行完全重启
- 保留重要的状态信息

### 3. 用户体验
- 及时通知用户重启状态
- 提供清晰的错误信息
- 允许用户手动触发重启

## 注意事项

1. **状态丢失**: 重启会清除团队的对话历史和中间状态
2. **性能影响**: 重启过程可能需要几秒钟时间
3. **并发安全**: 确保重启过程不与其他操作冲突
4. **资源管理**: 正确清理旧团队的资源

## 故障排除

### 常见问题

1. **重启失败**
   - 检查会话是否存在
   - 确认团队实例状态
   - 查看详细错误日志

2. **状态不一致**
   - 清理状态文件
   - 重新创建会话
   - 检查数据库连接

3. **前端未收到通知**
   - 确认WebSocket连接
   - 检查响应队列状态
   - 验证消息格式

通过这个完整的团队重启功能，您的系统现在具备了强大的错误恢复能力，可以在遇到问题时自动或手动重启团队，确保服务的持续可用性。
